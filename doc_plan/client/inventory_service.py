import grpc
from google.protobuf.json_format import MessageToDict
from hex_exception import UnAuthorized, exception_from_str
from doc_plan.proto.inventory import inventory_pb2
from doc_plan.proto.inventory.inventory_pb2_grpc import InventoryServiceStub
from doc_plan import APP_CONFIG, TIMEOUT, logger

_HOST = str(APP_CONFIG['inventory_host'])
_PORT = str(APP_CONFIG['inventory_port'])
_CHANNEL = grpc.insecure_channel(_HOST + ':' + _PORT, options=[
    ('grpc.max_send_message_length', 1024 * 1024 * 1024),
    ('grpc.max_receive_message_length', 1024 * 1024 * 1024),
], )


class InventoryService(object):
    """
    所有请求的patner_id和user_id在get_struct会从全局导入，或者自己单独传
    """

    def __init__(self):
        self.inventoryStub = InventoryServiceStub(_CHANNEL)

    # @result_wraps
    # @get_struct
    # 库存批处理请求，可以视为唯一的入口
    def deal_with_inventory(self, batch_no, code=None, action=None, description=None, detail=None, partner_id=None,
                            user_id=None, trace_id=None):

        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.inventoryStub.Batch(
                inventory_pb2.BatchRequest(batch_no=batch_no,
                                           code=code,
                                           action=action,
                                           description=description,
                                           detail=detail,
                                           trace_id=trace_id), timeout=200, metadata=metadata)

            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return entity


    def query_realtime_inventory_products_ids_by_branch_id(self, branch_id,
                                 offset=None, limit=None, page_size=None, total=True,
                                 aggregate=False, detail=False, snapshot=False,
                                 partner_id=None, user_id=None, product_ids=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        pagination = {"offset": offset, "limit": limit, "page_size": page_size, "total": total}
        options = {"aggregate": aggregate, "detail": detail, "snapshot": snapshot}
        try:
            if not isinstance(branch_id,list):
                branch_id = [branch_id]
            ret = self.inventoryStub.QueryBranchInventory(inventory_pb2.InventoryQueryRequest(
                branch_ids=inventory_pb2.InventoryQueryRequest.BranchIDs(branch_id=branch_id),
                pagination=pagination,
                options=options,
                product_ids=product_ids
            ), timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            result = []
            if entity and entity.get('rows'):
                for row in entity['rows']:
                    product_id = row['account'].get('product_id')
                    quantity_avail = row['amount'].get('qty') if row['amount'].get('qty') else 0
                    if quantity_avail:
                        result.append(int(product_id))
            return result
        except Exception as e:
            logger.error('获取实时库存失败{}'.format(e))
            raise e
    def query_realtime_inventory_by_branch_id(self, branch_id,
                                 offset=None, limit=None, page_size=None, total=True,
                                 aggregate=False, detail=False, snapshot=False,
                                 partner_id=None, user_id=None, product_ids=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        pagination = {"offset": offset, "limit": limit, "page_size": page_size, "total": total}
        options = {"aggregate": aggregate, "detail": detail, "snapshot": snapshot}
        try:
            if not isinstance(branch_id,list):
                branch_id = [branch_id]
            ret = self.inventoryStub.QueryBranchInventory(inventory_pb2.InventoryQueryRequest(
                branch_ids=inventory_pb2.InventoryQueryRequest.BranchIDs(branch_id=branch_id),
                pagination=pagination,
                options=options,
                product_ids=product_ids
            ), timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            result ={}
            if entity and entity.get('rows'):
                for row in entity['rows']:
                    product_id = row['account'].get('product_id')
                    quantity_avail = row['amount'].get('qty') if row['amount'].get('qty') else 0
                    result[int(product_id)] = float(quantity_avail)
            return result
        except Exception as e:
            logger.error('获取实时库存失败{}'.format(e))
            raise e


inventory_service = InventoryService()
