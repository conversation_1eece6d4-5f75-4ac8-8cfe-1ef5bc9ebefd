import math
import time
import json
import calendar
import copy

from doc_plan.model import doc_plan
from ..client.auth_permission import auth_permission
from ..error.exception import *
from doc_plan.model.plan_code import PlanCode
from doc_plan.client.metadata_service import metadata_service
from google.protobuf.timestamp_pb2 import Timestamp
from datetime import datetime, timedelta
from doc_plan.utils.helper import get_guid, convert_to_int, branch_list_scope_check, convert_to_decimal
from ..driver.mysql import session_maker
from doc_plan.model.doc_plan import DocBatchPlanDB, DocBatchPlanBranchDB, PlanDB
from doc_plan.model.demand.demand import Supply_demand, Supply_demand_product
from doc_plan.model.doc_code.supply_doc_code import Supply_doc_code
from doc_plan.model.doc_code.stocktake_month_code import Stocktake_month_code
from doc_plan.model.adjust.adjust import adjust_repository
from doc_plan.model.adjust.adjust import AdjustDB, AdjustDetailsDB, AdjustProductDB, AdjustLogDB
from doc_plan.model.stocktake.stocktake import SupplySTDocDB, SupplySTDocDetailsDB, SupplySTProductDB, SupplySTDocLogDB
from doc_plan.driver.mq import mq_producer
from doc_plan.utils.helper import MessageTopic
from doc_plan.client.supply_service import supply_service
from doc_plan.client.bom_service import Bom_service
from doc_plan.module.doc_plan_store import doc_plan_store_module
from sqlalchemy import or_
from doc_plan.client.inventory_service import inventory_service
from doc_plan.model import doc_plan_status
from doc_plan.module.utils import *
from doc_plan.model import doc_plan_position
from doc_plan.module.doc_plan_position import doc_plan_position_module
from doc_plan.utils.auth_mapping import auth_code_mapping
from doc_plan import logger


class DocPlanModule(object):
    """
    计划
    """

    def __init__(self):
        self.doc_plan_repo = doc_plan.DocPlanRepository()
        self.doc_plan_position_repo = doc_plan_position.DocPlanPositionRepository()
        self.doc_plan_status_repo = doc_plan_status.DocPlanStatusRepository()
        self.ST_DOC_TYPE = dict(D=1, W=2, M=3)

    def get_timestamp(self, value: datetime):
        timestamp = Timestamp()
        timestamp.FromDatetime(value)
        return timestamp

    def get_datetime(self, value):
        timestamp = Timestamp()
        timestamp.seconds = value.seconds
        date = timestamp.ToDatetime()
        if date == datetime(1970, 1, 1):
            return None
        return date

    def get_local_utc_offset(self):
        ts = time.time()
        utc_offset = int((datetime.fromtimestamp(ts) - datetime.utcfromtimestamp(ts)).total_seconds() / 3600)
        return utc_offset

    def get_week_of_month(self, year, month, day):
        """
        获取指定的某天是某个月中的第几周
        周一作为一周的开始
        """
        end = int(datetime(year, month, day).strftime("%W"))
        begin = int(datetime(year, month, 1).strftime("%W"))
        return end - begin + 1

    def get_date_range(self, start_date, end_date, delta_day, return_date=False):
        date_list = []
        while start_date <= end_date:
            if return_date is True:
                date_list.append(start_date)
            else:
                date_list.append(start_date.strftime('%Y-%m-%d'))
            start_date += timedelta(days=delta_day)
        return date_list

    def get_date_week_map(self, start_date: datetime, end_date: datetime):
        """return:
        {'2021-03-15': 0,
        '2021-03-16': 1}
        """
        date_week_map = {}
        while start_date <= end_date:
            date_week_map[start_date.strftime('%Y-%m-%d')] = start_date.weekday()
            start_date += timedelta(days=1)
        return date_week_map

    def get_products_by_warehouse_id(self, branch_id=None, return_all_products=None, partner_id=None, user_id=None):
        """封装方法方便调用，根据仓库id获取商品id列表"""
        product_ids = []
        products_of_warehouse = supply_service.get_product_list_by_WHid(branch_id=int(branch_id),
                                                                        return_all_products=return_all_products,
                                                                        partner_id=partner_id,
                                                                        user_id=user_id).get("rows")
        if isinstance(products_of_warehouse, list):
            for p in products_of_warehouse:
                if p.get("id") and p.get("id") not in product_ids:
                    product_ids.append(convert_to_int(p.get("id")))
        return product_ids

    def create_plan(self, request, partner_id=None, user_id=None):
        """
        创建计划(日type=D，周type=W，月type=M，不定期type=R)
        1.可选择所有门店(branch_method=ALL),或自己添加门店列表(branch_method=STORE)
        2.可以选择根据类(product_method=CATEGORY)或根据商品来盘点(product_method=PRODUCT)或者所有商品(product_method=ALL)　
        2.1 根据类别，可以添加类别列表
        2.2 根据商品，可添加商品列表
        3.选填提前几天生成单(before=x)
        4.日, 选填日期间隔（interval=x）
        5.周，必填周几(week_method='1'(周一),'2'(周二）.....)
        6.月，必填每月,日（day_method='1'(1号),'2'(2号)....'-2'(倒数2天),'-1'(倒数１天)）:
        """
        request_id = request.request_id
        if not request_id:
            raise NoRequestIDError('没有请求ID')
        # 验证该请求是否已创建
        plan = self.doc_plan_repo.get_plan_by_request_id(request_id)
        if plan:
            res = dict(
                plan_id=plan['id'],
                result=True
            )
            return res
        # user_name = None
        user_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        # todo 校验循环规则
        # 预先更新start_before(start - before), 方便查询索引
        if request.start and request.before:
            start_before = self.get_datetime(request.start) + timedelta(days=-request.before)
        else:
            start_before = self.get_datetime(request.start)
        # 时区
        tz = request.tz
        if tz != "":
            tz = int(tz)
        else:
            tz=8
        start_before = start_before + timedelta(hours=tz)
        start_before = datetime(start_before.year, start_before.month, start_before.day)
        plan = dict(
            id=get_guid(),
            request_id=request.request_id,
            plan_type=request.plan_type,
            # 提前量
            before=request.before,
            # //是否计算库存
            calculate_inventory=request.calculate_inventory,
            # //计划编码
            code=request.code,
            # //计划名字
            name=request.name,
            # //整个计划循环规则信息
            # // 循环类型
            method=request.method,
            # //月循环规则
            month_method=request.month_method,
            # //循环规则对应未来规则间隔
            sub_month_method=request.sub_month_method,
            # //周循环规则
            week_method=request.week_method,
            # //循环规则对应未来规则
            sub_week_method=request.sub_week_method,
            # //日循环规则
            day_method=request.day_method,
            # //循环规则对应未来规则间隔
            sub_day_method=request.sub_day_method,
            interval=request.interval,
            # //整个计划循环规则信息
            branch_method=request.branch_method,
            product_method=request.product_method,
            remark=request.remark,
            # //计划开始时间
            start=self.get_datetime(request.start),
            # //计划结束时间
            end=self.get_datetime(request.end),
            created_name=user_name,
            updated_name=user_name,
            partner_id=partner_id,
            created_by=user_id,
            updated_by=user_id,
            status='INIT',
            doc_deadline=self.get_datetime(request.doc_deadline),
            doc_cancel=self.get_datetime(request.doc_cancel),
            start_before=start_before,
            is_recreate=request.is_recreate,
            sub_type=request.sub_type,
            store_type=request.store_type,
            extend=request.extend,
            tz=tz,
            calc_by_safety_qty=request.calc_by_safety_qty,
        )
        # print('plan', plan)
        if not plan['code']:
            plan['code'] = PlanCode.get_code(partner_id)
        plan = [plan]
        store_ids = []
        domain = auth_code_mapping.get_full_domain(request.domain)
        # 全部门店不需要生成快照（行不通）
        if request.branch_method == "ALL":
            if request.store_type == "MACHINING_CENTER":
                store_info = metadata_service.list_machining_center(filters={"status__in": ["ENABLED"]},
                                                                    return_fields="id",
                                                                    partner_id=partner_id,
                                                                    user_id=user_id).get('rows', [])
            elif request.store_type == "WAREHOUSE":
                store_info = metadata_service.get_distribution_center_list(filters={"status__in": ["ENABLED"]},
                                                                           return_fields="id",
                                                                           ids=store_ids, partner_id=partner_id,
                                                                           user_id=user_id).get('rows', [])
            else:
                store_info = metadata_service.get_store_list(filters={"status__in": ["ENABLED"],
                                                                      "open_status__in": ["OPENED"]},
                                                             return_fields="id",
                                                             partner_id=partner_id, user_id=user_id).get('rows', [])
                # 全部门店时需要校验门店权限
                branch_scope = auth_permission.list_data_scope_check(partner_id=partner_id, user_id=user_id,
                                                                     resource_schema='store',
                                                                     domain=domain)

                if branch_scope.get('full_access'):
                    pass
                else:
                    scope_ids = branch_scope.get('ids', [])
                    if not scope_ids:
                        raise StoreScopeException("当前用户没有任何数据权限！")
                    for store in store_info:
                        if store.get('id') not in scope_ids:
                            store_info.remove(store)

            for store in store_info:
                store_ids.append(convert_to_int(store.get('id')))
            # 用1来标记一条全部门店，为了保存store_type
            # store_ids = [1]
        else:
            store_ids = list(request.store_ids)

        # 全部商品不生成快照
        # if request.product_method == "All":
        #     product_info = metadata_service.get_product_list(filters={"status": "ENABLED"}, return_fields="id",
        #                                                      partner_id=partner_id, user_id=user_id).get('rows', [])
        #     product_ids = [convert_to_int(p.get('id')) for p in product_info]
        # else:
        product_ids = list(request.product_ids)
        store_ids = set(store_ids)
        product_ids = set(product_ids)
        branch_ids = set(list(request.branch_ids))
        category_ids = set(list(request.category_ids))
        categories = []
        products = []
        branches = []
        stores = []
        ####
        for s in store_ids:
            store = dict(
                store_id=s,
                plan_id=plan[0]['id'],
                deleted=False,
                created_by=user_id,
                updated_by=user_id,
                created_name=user_name,
                updated_name=user_name,
                store_type=request.store_type,
                partner_id=partner_id,
            )
            stores.append(store)
        for b in branch_ids:
            branch = dict(
                branch_id=b,
                plan_id=plan[0]['id'],
                deleted=False,
                created_by=user_id,
                updated_by=user_id,
                created_name=user_name,
                updated_name=user_name,
                # 区域录入方式
                branch_type=request.branch_type,
                partner_id=partner_id,
            )
            branches.append(branch)
        for p in product_ids:
            product = dict(
                product_id=p,
                plan_id=plan[0]['id'],
                deleted=False,
                created_by=user_id,
                updated_by=user_id,
                created_name=user_name,
                updated_name=user_name,
                partner_id=partner_id,
            )
            products.append(product)
        for c in category_ids:
            category = dict(
                category_id=c,
                plan_id=plan[0]['id'],
                deleted=False,
                created_by=user_id,
                updated_by=user_id,
                created_name=user_name,
                updated_name=user_name,
                partner_id=partner_id,
            )
            categories.append(category)
        self.doc_plan_repo.create_plan(plan, categories=categories, products=products, branches=branches, stores=stores)
        res = dict(
            plan_id=plan[0]['id'],
            result=True
        )
        return res

    def get_plan_by_id(self, request, partner_id, user_id):
        plan_id = request.plan_id
        include_product = request.include_product
        include_category = request.include_category
        include_branch = request.include_branch
        include_store = request.include_store
        store_type = request.store_type
        domain = auth_code_mapping.get_full_domain(request.domain)
        is_check_scope = True
        if request.domain in ["store", "whs", "mfy", "frs-store"]:
            is_check_scope = False
        plan_props, product_props, category_props, branch_props, store_props = self.doc_plan_repo.get_plan_by_id(
            plan_id, partner_id,
            include_product=include_product,
            include_category=include_category,
            include_branch=include_branch,
            include_store=include_store)
        tz = plan_props.get('tz',0)
        plan_props['tz'] = str(tz)
        if category_props:
            category_ids = [ca['category_id'] for ca in category_props]
            category_list_ret = metadata_service.get_product_category_list(return_fields="id,code,name",
                                                                           ids=category_ids,
                                                                           partner_id=partner_id, user_id=user_id)

            category_dict = {}
            if category_list_ret:
                for row in category_list_ret['rows']:
                    category_dict[int(row['id'])] = [row.get('code'), row.get('name')]
            for c in category_props:
                c['category_code'] = category_dict[c['category_id']][0] if c[
                                                                               'category_id'] in category_dict.keys() else ''
                c['category_name'] = category_dict[c['category_id']][1] if c[
                                                                               'category_id'] in category_dict.keys() else ''
        if product_props:
            product_ids = [pr['product_id'] for pr in product_props]
            product_list_ret = metadata_service.get_product_list(return_fields="id,code,name",
                                                                 ids=product_ids,
                                                                 partner_id=partner_id, user_id=user_id)

            product_dict = {}
            if product_list_ret:
                for row in product_list_ret['rows']:
                    product_dict[int(row['id'])] = [row.get('code'), row.get('name')]
            for p in product_props:
                p['product_code'] = product_dict[p['product_id']][0] if p['product_id'] in product_dict else None
                p['product_name'] = product_dict[p['product_id']][1] if p['product_id'] in product_dict else None
        if branch_props:
            branch_ids = [br['branch_id'] for br in branch_props]
            branch_type = branch_props[0]['branch_type']
            if not branch_type or branch_type == "STORE":
                branch_type = 'branch_region'
            if is_check_scope is True:
                branch_ids = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema=branch_type,
                                                     domain=domain, branch_ids=branch_ids)
            plan_props['branch_type'] = branch_type
            str_ = "metadata_service.get_{}_list(return_fields='id,code,name',ids=branch_ids" \
                  ", partner_id=partner_id,user_id=user_id)".format(branch_type)
            branch_list_ret = eval(str_)
            branch_dict = {}
            if branch_list_ret:
                for row in branch_list_ret['rows']:
                    branch_dict[int(row['id'])] = [row.get('code'), row.get('name')]
            for b in branch_props:
                b['branch_code'] = branch_dict[b['branch_id']][0] if b['branch_id'] in branch_dict.keys() else ''
                b['branch_name'] = branch_dict[b['branch_id']][1] if b['branch_id'] in branch_dict.keys() else ''
                b['branch_type'] = branch_type
        if store_props:
            store_ids = [st['store_id'] for st in store_props]
            if not store_type:
                store_type = store_props[0].get("store_type")
            if store_type == "WAREHOUSE":
                store_list_ret = metadata_service.get_distribution_center_list(return_fields="id,code,name",
                                                                               ids=store_ids, partner_id=partner_id,
                                                                               user_id=user_id)
            elif store_type == "MACHINING_CENTER":
                store_list_ret = metadata_service.list_machining_center(filters={"status__in": ["ENABLED"]},
                                                                        partner_id=partner_id,
                                                                        user_id=user_id)
            else:
                if is_check_scope is True:
                    store_ids = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                                        domain=domain, branch_ids=store_ids)
                store_list_ret = metadata_service.get_store_list(return_fields="id,code,name",
                                                                 ids=store_ids, partner_id=partner_id,
                                                                 user_id=user_id)
            store_dict = {}
            if store_list_ret:
                for row in store_list_ret['rows']:
                    store_dict[int(row['id'])] = [row.get('code'), row.get('name')]
            for b in store_props:
                b['store_code'] = store_dict[b['store_id']][0] if b['store_id'] in store_dict.keys() else ''
                b['store_name'] = store_dict[b['store_id']][1] if b['store_id'] in store_dict.keys() else ''
        plan_props['plan_product'] = product_props
        plan_props['plan_category'] = category_props
        plan_props['plan_branch'] = branch_props
        plan_props['plan_store'] = store_props
        return plan_props

    def get_plan_product_by_id(self, request, partner_id, user_id):
        """根据计划查询计划里配置商品"""
        result = dict()
        plan_id = request.plan_id
        return_fields = request.return_fields
        limit = request.limit
        offset = request.offset
        plan_props, product_props, category_props, _, _ = self.doc_plan_repo.get_plan_by_id(
            plan_id, partner_id,
            include_product=True,
            include_category=True)
        product_method = plan_props.get('product_method')
        if not return_fields:
            return_fields = "id,code,name"
        if product_method == "ALL":
            products = metadata_service.get_product_list(filters={"status": "ENABLED"},
                                                         limit=limit,
                                                         offset=offset,
                                                         return_fields=return_fields,
                                                         include_total=True,
                                                         partner_id=partner_id, user_id=user_id)
            result['total'] = products.get('total')
            product_info = products.get('rows', [])
        else:
            product_info = []
            category_ids = [c.get('category_id') for c in category_props]
            product_ids = [p.get('product_id') for p in product_props]
            if category_ids:
                relation_filters = {'product_category': category_ids}
                products_c = metadata_service.get_product_list(filters={"status": "ENABLED"},
                                                               relation_filters=relation_filters,
                                                               return_fields="id",
                                                               partner_id=partner_id, user_id=user_id).get('rows', [])
                for pc in products_c:
                    product_ids.append(convert_to_int(pc.get('id')))
            if product_ids:
                products = metadata_service.get_product_list(filters={"status": "ENABLED"},
                                                             limit=limit,
                                                             offset=offset,
                                                             ids=product_ids,
                                                             return_fields=return_fields,
                                                             partner_id=partner_id, user_id=user_id).get('rows', [])
                product_info.extend(products)
            result['total'] = len(product_info)
        for p in product_info:
            if 'id' in p.keys():
                p['id'] = int(p['id'])
        result['rows'] = product_info
        return result

    def get_plan(self, request, partner_id, user_id):
        res = {}
        plan_type = request.plan_type if request.plan_type else auth_code_mapping.PLAN_TYPE_MAPPING_MODULE.get(
            request.module)
        store_ids = list(request.store_ids)
        sub_type = request.sub_type
        # branch_ids = list(request.branch_ids)
        branch_ids = []
        status = list(request.status)
        offset = request.offset
        limit = request.limit
        method = request.method
        is_recreate = request.is_recreate
        store_type = request.store_type
        # logger.info("plan_type: {} store_ids: {} offset: {} limit: {}".format(plan_type, store_ids, offset, limit))
        # 计划管理区域过滤，将store_id置换出branch_id 与store OR 查询
        if len(store_ids) > 0 and store_type in ["STORE", "FRS_STORE"]:
            store_info = metadata_service.get_store_list(ids=store_ids,
                                                         partner_id=partner_id,
                                                         user_id=user_id).get('rows', [])
            for s in store_info:
                if s.get('branch_region'):
                    branch_ids.append(int(s['branch_region'][0]))
        count, list_plan = self.doc_plan_repo.get_plan(store_ids, branch_ids, partner_id, plan_type=plan_type,
                                                       status=status, store_type=store_type, sub_type=sub_type,
                                                       method=method, is_recreate=is_recreate, offset=offset,
                                                       limit=limit)
        res['rows'] = []
        for plan in list_plan:
            plan['tz'] = str(plan['tz'])
            res['rows'].append(plan)
        res['total'] = count
        # res['rows'] = list_plan
        return res

    def put_plan(self, request, partner_id, user_id):
        res = {}
        plan_id = request.plan_id
        if not plan_id:
            raise NoRequestIDError('没有计划ID')
        # 验证该plan是否已创建
        plan_obj = self.doc_plan_repo.get_plan_by_id(plan_id, partner_id)
        if not plan_obj:
            raise DataValidationException('没有该计划')
        plan_obj = plan_obj[0]
        if plan_obj.get('status') != 'INIT':
            raise DataValidationException('计划更新必须为新建')
        only_remark = request.only_remark
        remark = request.remark
        user_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        if only_remark:
            self.doc_plan_repo.update_plan_remark(plan_id, remark, partner_id=partner_id,
                                                  user_id=user_id, username=user_name)
            res['result'] = True
            return res
        # 预先更新start_before(start - before), 方便查询索引
        if request.start and request.before:
            start_before = self.get_datetime(request.start) + timedelta(days=-request.before)
        else:
            start_before = self.get_datetime(request.start)
        # 时区
        tz = request.tz
        if tz != "":
            tz = int(tz)
        else:
            tz=8
        start_before = start_before + timedelta(hours=tz)
        start_before = datetime(start_before.year, start_before.month, start_before.day)
        logger.info('----start_before---:')
        logger.info(start_before)
        logger.info('----request.calc_by_safety_qty---')
        logger.info(request.calc_by_safety_qty)
        plan = dict(
            # 计划id
            id=request.plan_id,
            # 提前量
            before=request.before,
            # //是否计算库存
            calculate_inventory=request.calculate_inventory,
            # //计划名字
            name=request.name,
            # //整个计划循环规则信息
            # // 循环类型
            method=request.method,
            # //月循环规则
            month_method=request.month_method,
            # //循环规则对应未来规则间隔
            sub_month_method=request.sub_month_method,
            # //周循环规则
            week_method=request.week_method,
            # //循环规则对应未来规则
            sub_week_method=request.sub_week_method,
            # //日循环规则
            day_method=request.day_method,
            # //循环规则对应未来规则间隔
            sub_day_method=request.sub_day_method,
            interval=request.interval,
            # //整个计划循环规则信息
            branch_method=request.branch_method,
            product_method=request.product_method,
            remark=request.remark,
            # //计划开始时间
            start=self.get_datetime(request.start),
            # //计划结束时间
            end=self.get_datetime(request.end),
            updated_name=user_name,
            doc_deadline=self.get_datetime(request.doc_deadline),
            doc_cancel=self.get_datetime(request.doc_cancel),
            start_before=start_before,
            is_recreate=request.is_recreate,
            sub_type=request.sub_type,
            store_type=request.store_type,
            extend=request.extend,
            calc_by_safety_qty=request.calc_by_safety_qty
        )
        if request.tz is not None:
            plan.update({"tz": request.tz})
        # 全部门店
        store_ids = []
        domain = auth_code_mapping.get_full_domain(request.domain)
        if request.branch_method == "ALL":
            if request.store_type == "MACHINING_CENTER":
                store_info = metadata_service.list_machining_center(filters={"status__in": ["ENABLED"]},
                                                                    return_fields="id",
                                                                    partner_id=partner_id,
                                                                    user_id=user_id).get('rows', [])
            elif request.store_type == "WAREHOUSE":
                store_info = metadata_service.get_distribution_center_list(filters={"status__in": ["ENABLED"]},
                                                                           return_fields="id",
                                                                           ids=store_ids, partner_id=partner_id,
                                                                           user_id=user_id).get('rows', [])
            else:
                store_info = metadata_service.get_store_list(filters={"status__in": ["ENABLED"],
                                                                      "open_status__in": ["OPENED"]},
                                                             return_fields="id",
                                                             partner_id=partner_id, user_id=user_id).get('rows', [])
                # 全部门店时需要校验门店权限
                branch_scope = auth_permission.list_data_scope_check(partner_id=partner_id, user_id=user_id,
                                                                     resource_schema='store',
                                                                     domain=domain)

                if branch_scope.get('full_access'):
                    pass
                else:
                    scope_ids = branch_scope.get('ids', [])
                    if not scope_ids:
                        raise StoreScopeException("当前用户没有任何数据权限！")
                    for store in store_info:
                        if store.get('id') not in scope_ids:
                            store_info.remove(store)
            for store in store_info:
                store_ids.append(convert_to_int(store.get('id')))
        else:
            store_ids = list(request.store_ids)
        # 全部商品
        # if request.product_method == "ALL":
        #     product_info = metadata_service.get_product_list(filters={"status": "ENABLED"}, return_fields="id",
        #                                                      partner_id=partner_id, user_id=user_id).get('rows', [])
        #     product_ids = [convert_to_int(p.get('id')) for p in product_info]
        # else:
        product_ids = list(request.product_ids)
        store_ids = set(store_ids)
        product_ids = set(product_ids)
        branch_ids = set(list(request.branch_ids))
        category_ids = set(list(request.category_ids))
        categories = []
        products = []
        branches = []
        stores = []
        ####
        for s in store_ids:
            store = dict(
                store_id=s,
                plan_id=plan['id'],
                deleted=False,
                created_by=user_id,
                updated_by=user_id,
                created_name=user_name,
                updated_name=user_name,
                store_type=request.store_type,
                partner_id=partner_id,
            )
            stores.append(store)
        for b in branch_ids:
            branch = dict(
                branch_id=b,
                plan_id=plan['id'],
                deleted=False,
                created_by=user_id,
                updated_by=user_id,
                created_name=user_name,
                updated_name=user_name,
                partner_id=partner_id,
                # 区域录入方式
                branch_type=request.branch_type
            )
            branches.append(branch)
        for p in product_ids:
            product = dict(
                product_id=p,
                plan_id=plan['id'],
                deleted=False,
                created_by=user_id,
                updated_by=user_id,
                created_name=user_name,
                updated_name=user_name,
                partner_id=partner_id,
            )
            products.append(product)
        for c in category_ids:
            category = dict(
                category_id=c,
                plan_id=plan['id'],
                deleted=False,
                created_by=user_id,
                updated_by=user_id,
                created_name=user_name,
                updated_name=user_name,
                partner_id=partner_id,
            )
            categories.append(category)

        res['result'] = self.doc_plan_repo.update_plan(plan_id, plan, partner_id=partner_id, user_id=user_id,
                                                       categories=categories, products=products, branches=branches,
                                                       stores=stores, user_name=user_name)
        return res

    def get_plan_by_store_ids(self, request, partner_id=None, user_id=None):
        res = {}
        plan_type = [request.plan_type] if request.plan_type else [
            auth_code_mapping.PLAN_TYPE_MAPPING_MODULE.get(request.module)]
        store_ids = list(request.store_ids)
        # branch_ids = list(request.branch_ids)
        sub_type = request.sub_type
        store_type = request.store_type
        branch_ids = []
        status = list(request.status)
        offset = request.offset
        limit = request.limit
        method = request.method
        is_recreate = request.is_recreate
        # 计划管理区域过滤，将store_id置换出branch_id 与store OR 查询
        store_dict = {}
        if len(store_ids) > 0:
            if store_type == "WAREHOUSE":
                store_info = metadata_service.get_distribution_center_list(ids=store_ids, partner_id=partner_id,
                                                                           user_id=user_id,
                                                                           return_fields='id,code,name',
                                                                           ).get('rows', [])
            elif store_type == "MACHINING_CENTER":
                store_info = metadata_service.list_machining_center(filters={"status__in": ["ENABLED"]},
                                                                    partner_id=partner_id,
                                                                    return_fields='id,code,name',
                                                                    user_id=user_id).get('rows', [])
            else:
                store_info = metadata_service.get_store_list(ids=store_ids,
                                                             partner_id=partner_id,
                                                             return_fields='id,code,name',
                                                             user_id=user_id).get('rows', [])
            for s in store_info:
                store_dict[int(s['id'])] = [s.get('code'), s.get('name')]
        list_plan_db, store_ids_db, branch_ids_db = self.doc_plan_repo.get_plan_by_store_ids(store_ids,
                                                                                             branch_ids,
                                                                                             partner_id,
                                                                                             plan_type=plan_type,
                                                                                             status=status,
                                                                                             method=method,
                                                                                             is_recreate=is_recreate,
                                                                                             offset=offset,
                                                                                             limit=limit,
                                                                                             store_type=store_type,
                                                                                             sub_type=sub_type)
        list_plan = []
        store_map = {}
        branch_type_ids = {}
        # store_ids_db ,计划id与store_id ,branch_ids_db 计划ID与branch_id
        for store in store_ids_db:
            if store[1] in store_map:
                if store[0] not in store_map[store[1]]:
                    store_map[store[1]].append(store[0])
            else:
                store_map[store[1]] = [store[0]]
        branch_map = {}
        # 根据区域类型查询主档
        for branch in branch_ids_db:
            if branch[1] in branch_map:
                if branch[0] not in branch_map[branch[1]]:
                    branch_map[branch[1]].append(branch[0])
            else:
                branch_map[branch[1]] = [branch[0]]
            if branch[2] in branch_type_ids:
                branch_type_ids[branch[2]].append(branch[0])
            else:
                branch_type_ids[branch[2]] = [branch[0]]
        store_of_branch_dict = {}
        for branch_type, branches in branch_type_ids.items():
            relation_filters = {str(branch_type): [str(b) for b in branches]}
            store_of_branch = metadata_service.get_store_list(partner_id=partner_id, user_id=user_id,
                                                              return_fields="id,{}".format(branch_type),
                                                              relation_filters=relation_filters,
                                                              ).get('rows', [])
            for store in store_of_branch:
                branch_id = convert_to_int(store.get(branch_type)[0]) if store.get(branch_type) else None
                if branch_id in store_of_branch_dict:
                    store_of_branch_dict[branch_id].append(convert_to_int(store.get('id')))
                else:
                    store_of_branch_dict[branch_id] = [convert_to_int(store.get('id'))]
        for plan in list_plan_db:
            store_list = store_map.get(plan['id'], [])
            branch_list = branch_map.get(plan['id'], [])
            store_info_list = []
            for branch_id in branch_list:
                store_list += store_of_branch_dict.get(branch_id, [])
            store_list = list(set(store_list))
            for s in store_list:
                if s in store_ids:
                    info = store_dict.get(s)
                    if info:
                        store_info_list.append(dict(
                            code=info[0],
                            name=info[1]
                        ))
            if len(store_info_list) == 0:
                continue
            plan["store_info"] = store_info_list
            list_plan.append(plan)
        res['total'] = len(list_plan)
        res['rows'] = list_plan
        return res

    def action_plan(self, request, partner_id, user_id):
        """
         确认，取消，回退一个计划:
         action = 'confirm' OR 'cancel' OR 'callback'
        """
        res = {}
        action = request.action
        plan_id = request.plan_id
        user_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        # user_name = None
        if action == 0:
            res["result"] = self.doc_plan_repo.lock_plan_status(plan_id, ['INIT'], 'CONFIRM', partner_id, user_name)
        elif action == 1:
            res["result"] = self.doc_plan_repo.lock_plan_status(plan_id, [], 'CANCELL', partner_id, user_name)
        elif action == 2:
            res["result"] = self.doc_plan_repo.lock_plan_status(plan_id, ['CONFIRM'], 'INIT', partner_id, user_name)
        else:
            raise ActionException('操作失败')
        return res

    def create_doc_batch(self, request, partner_id, user_id):
        res = {}
        plan_type = list(request.plan_type)
        sub_type = list(request.sub_type)
        self.init_plan_batch(plan_type, sub_type, partner_id, user_id)
        return res

    def calculate_doc_plan_next_time(self, plan_date, plan_obj_list):
        pass

    def init_plan_batch(self, plan_type, sub_type, partner_id, user_id, plan_date_utc=None, tz=0, interval=0):
        """
        根据有效的计划，初始化单据生成批次,e.g.:若有效计划数为3,则生成1个主批次和3个副批次
        :param plan_type: 计划类型-D(日)、W(周)、M(月)
        :param sub_type: 计划子类型-R(不定期)
        :param partner_id:
        :param user_id:
        :param plan_date_utc
        :param tz: 时间偏移量
        :param interval 计划轮询间隔
        :return:
        """
        if not plan_date_utc:
            plan_date_time = datetime.utcnow()
        else:
            plan_date_time = plan_date_utc
        # 订单日期运算--- ！！！！！！
        logger.info("plan_date_time：{}".format(plan_date_time))
        calculate_plan_date = plan_date_time + timedelta(hours=tz)
        plan_date = datetime(int(calculate_plan_date.year), int(calculate_plan_date.month),
                             int(calculate_plan_date.day)) - timedelta(hours=tz)
        # 是否已经初始化批次
        valid_plan_obj_list = []
        # calculate_date = datetime(int(plan_date_time.year), int(plan_date_time.month), int(plan_date_time.day))
        calculate_date = datetime(int(calculate_plan_date.year), int(calculate_plan_date.month),
                                  int(calculate_plan_date.day))
        calculate_end_date = plan_date
        logger.info("calculate_date：{}==({})".format(calculate_date,tz))
        logger.info("calculate_end_date：{}==({})".format(calculate_end_date,tz))
        plan_obj_list, is_interval_schedule_ids = \
            self.doc_plan_repo.list_valid_plan(plan_type, sub_type, partner_id, calculate_date, calculate_end_date, tz,
                                               interval)
        pr_valid_plan_list = [(v.id, v.method, v.sub_type) for v in plan_obj_list]
        logger.info('当前时间合法的计划列表：{}--隔天补偿计划：{}'.format(pr_valid_plan_list, is_interval_schedule_ids))
        if isinstance(plan_obj_list, list) and len(plan_obj_list) > 0:
            for plan_obj in plan_obj_list:
                # 业务循环运算日期---calculate_plan_date ！！！！！
                target_date = calculate_plan_date.date()
                plan_date_start = plan_obj.start + timedelta(hours=tz)
                plan_date_end = plan_obj.end + timedelta(hours=tz)
                if plan_obj.before:
                    target_date = target_date + timedelta(days=plan_obj.before)
                logger.info("plan_obj-id：{}---target_date：{}".format(plan_obj.id, target_date))
                if target_date > plan_date_end.date():
                    continue
                # 日循环规则
                if plan_obj.method == 'D' and plan_obj.sub_type != 'R':
                    # 如果有间隔天数，则需要从 day_method 开始计算，看是否刚好满足间隔条件
                    if plan_obj.interval and plan_obj.interval > 0:
                        if (target_date - plan_date_start.date()).days < 0:
                            continue
                        day_method = plan_obj.day_method[:10]
                        if day_method:
                            # 判断起始量
                            start_date = datetime.strptime(day_method, '%Y-%m-%d').date()
                            days = (target_date - start_date).days
                        else:
                            days = (target_date - plan_date_start.date()).days
                        if days % (plan_obj.interval + 1) == 0:
                            valid_plan_obj_list.append(plan_obj)
                    else:
                        valid_plan_obj_list.append(plan_obj)
                # 周循环规则
                elif plan_obj.method == 'W' and plan_obj.sub_type != 'R':
                    # 时间的weekday是0,1,2...., 而types service里面的week_method是'1000001'
                    # 0100000-2
                    week_day = target_date.weekday()
                    # 查看盘点日期的weekday(周几)是不是等于盘点计划里面的week_method
                    week_method = plan_obj.week_method
                    # 获取当前日期属于这个月的第几周
                    week_month_now = self.get_week_of_month(target_date.year, target_date.month, target_date.day)
                    week_month = week_month_now
                    if week_method and "-" in week_method:
                        week_method, week_month = week_method.split('-')
                    bool_week_method = week_method[week_day]
                    if bool_week_method == '1' and int(week_month) == week_month_now:
                        valid_plan_obj_list.append(plan_obj)
                # 月循环规则
                elif plan_obj.method == 'M' and plan_obj.sub_type != 'R':
                    month_method = plan_obj.month_method
                    if month_method is None:
                        continue
                    month_method_list = [int(c) for c in month_method.split(',')]
                    _, monthRange = calendar.monthrange(target_date.year, target_date.month)
                    # 月末倒数天数计算，是否是倒数第3，2，1天也满足
                    interval_range = target_date.day - monthRange - 1
                    if target_date.day in month_method_list:
                        valid_plan_obj_list.append(plan_obj)
                    elif interval_range in month_method_list:
                        valid_plan_obj_list.append(plan_obj)
                # 不定期循环规则
                elif plan_obj.sub_type == 'R' and target_date == (plan_obj.start + timedelta(hours=tz)).date():
                    valid_plan_obj_list.append(plan_obj)
                # # 新不定期规则（子业务不定期规则,适应新不定期盘点）
                # if plan_obj.sub_type in sub_type and target_date == (plan_obj.start + timedelta(hours=tz)).date():
                #     valid_plan_obj_list.append(plan_obj)
        pr_valid_plan_obj_list = [(v.id, v.method, v.sub_type) for v in valid_plan_obj_list]
        logger.info('----最终能执行的计划:%s' % pr_valid_plan_obj_list)
        batch_plans = []
        update_plan_obj_list = []
        insert_batch_plans_list = []
        db_session = session_maker()
        try:
            for plan_obj in valid_plan_obj_list:
                try:
                    last_time = calculate_date
                    # 隔天补偿
                    if plan_obj.id in is_interval_schedule_ids:
                        last_time = calculate_date - timedelta(days=1)
                    update_plan_obj = dict(
                        id=plan_obj.id,
                        last_time=last_time,
                        updated_at=plan_obj.updated_at,
                        updated_name=plan_obj.updated_name
                    )
                    # update_plan_obj_list.append(update_plan_obj)
                    batch_plan_db = dict(
                        id=get_guid(),
                        batch_id=plan_obj.id,
                        partner_id=partner_id,
                        user_id=user_id,
                        process_status='INIT',
                        plan_date=plan_date,
                        plan_id=plan_obj.id,
                        plan_type=plan_obj.plan_type,
                        method=plan_obj.method,
                    )
                    batch_plans.append(dict(batch_id=batch_plan_db['id'], plan_id=batch_plan_db['plan_id'],
                                            plan_type=plan_obj.plan_type, plan_batch_id=batch_plan_db['id'],
                                            method=plan_obj.method, partner_id=partner_id, user_id=user_id))
                    # insert_batch_plans_list.append(batch_plan_db)
                    db_session.bulk_update_mappings(PlanDB, [update_plan_obj])
                    db_session.bulk_insert_mappings(DocBatchPlanDB, [batch_plan_db])
                    db_session.commit()
                except Exception as e:
                    logger.error("init_plan_batch error: {}".format(e))
                    db_session.rollback()
            # db_session.bulk_update_mappings(PlanDB, update_plan_obj_list)
            # db_session.bulk_insert_mappings(DocBatchPlanDB, insert_batch_plans_list)
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()
        for batch_plan in batch_plans:
            mq_producer.publish('boh_order', MessageTopic.DOC_PLAN_STORE_INIT_TOPIC, batch_plan)
        return True

    def init_plan_store_batch(self, plan_batch_id, batch_id, plan_id, partner_id, plan_type, method,
                              user_id, **kwargs):
        """
        根据单据批次生成门店单据批次，每个门店一个批次
        :param plan_batch_id: 计划批次id,
        :param batch_id: 主批次id
        :param plan_id: 计划id
        :return:
        """
        # 锁记录
        batch_plan = self.doc_plan_repo.lock_plan_batch_process_status(plan_batch_id, batch_id,
                                                                       plan_id,
                                                                       ['INIT', 'PROCESSING'], 'PROCESSING',
                                                                       partner_id=partner_id)
        if not batch_plan:
            return True
        plan, _, _, branches, stores = self.doc_plan_repo.get_plan_by_id(batch_plan.plan_id, partner_id,
                                                                         include_branch=True, include_store=True)
        if not plan:
            # 无法找到计划则任务失败，不重新执行
            self.doc_plan_repo.lock_plan_batch_process_status(plan_batch_id, batch_id, plan_id,
                                                              'PROCESSING',
                                                              'FAIL',
                                                              reason='Invalid plan_id:%s' % plan_id,
                                                              partner_id=partner_id)
            return True
        plan_stores = []
        if len(stores) > 0:
            store_type = stores[0].get("store_type")
            if plan.get("branch_method") == "ALL":
                if store_type == "WAREHOUSE":
                    store_info_ret = metadata_service.get_distribution_center_list(filters={"status__in": ["ENABLED"]},
                                                                                   partner_id=partner_id,
                                                                                   user_id=user_id).get('rows', [])
                elif store_type == "MACHINING_CENTER":
                    store_info_ret = metadata_service.list_machining_center(filters={"status__in": ["ENABLED"]},
                                                                            partner_id=partner_id,
                                                                            user_id=user_id).get('rows', [])
                else:
                    store_info_ret = metadata_service.get_store_list(filters={"open_status__in": ["OPENED"],
                                                                              "status__in": ["ENABLED"]},
                                                                     partner_id=partner_id, user_id=user_id,
                                                                     ).get('rows', [])
            else:
                # 获取计划里面的门店
                st_ids = []
                for st in stores:
                    st_ids.append(st.get('store_id'))
                if store_type == "WAREHOUSE":
                    store_info_ret = metadata_service.get_distribution_center_list(ids=st_ids,
                                                                                   filters={"status__in": ["ENABLED"]},
                                                                                   partner_id=partner_id,
                                                                                   user_id=user_id).get('rows', [])
                elif store_type == "MACHINING_CENTER":
                    store_info_ret = metadata_service.list_machining_center(ids=st_ids,
                                                                            filters={"status__in": ["ENABLED"]},
                                                                            partner_id=partner_id,
                                                                            user_id=user_id).get('rows', [])
                else:
                    store_info_ret = metadata_service.get_store_list(ids=st_ids, filters={
                        "open_status__in": ["OPENED"],
                        "status__in": ["ENABLED"]
                    }, partner_id=partner_id, user_id=user_id).get('rows', [])
            if len(store_info_ret) > 0:
                for st_info in store_info_ret:
                    if int(st_info['id']) not in plan_stores:
                        plan_stores.append(int(st_info['id']))
        if len(branches) > 0:
            # 支持各种区域
            region_filters = 'branch_region'
            branch_type = branches[0].get('branch_type')
            if branch_type in ['branch_region', 'geo_region', 'order_region', 'distribution_region', 'bom_region',
                               'attribute_region', 'transfer_region', 'market_region', 'purchase_region',
                               'franchisee_region']:
                region_filters = branch_type
            relation_filters = {region_filters: [str(branch['branch_id']) for branch in branches if
                                                 branch.get('branch_id')]}

            store_list_ret = metadata_service.get_store_list(filters={"open_status__in": ["OPENED"],
                                                                      "status__in": ["ENABLED"]},
                                                             relation_filters=relation_filters,
                                                             partner_id=partner_id, user_id=user_id,
                                                             ).get('rows', [])
            print('store_list_ret', store_list_ret)
            if len(store_list_ret) > 0:
                for store in store_list_ret:
                    if int(store['id']) not in plan_stores:
                        plan_stores.append(int(store['id']))
        batch_branches = []
        db_session = session_maker()
        try:
            db_session.query(DocBatchPlanDB).filter(DocBatchPlanDB.id == batch_plan.id).update(
                {DocBatchPlanDB.process_status: 'PROCESS',
                 DocBatchPlanDB.updated_at: datetime.utcnow(),
                 DocBatchPlanDB.reason: ''})
            if isinstance(plan_stores, list):
                for store_id in plan_stores:
                    batch_branch_db = DocBatchPlanBranchDB()
                    batch_branch_db.id = get_guid()
                    batch_branch_db.batch_id = batch_plan.batch_id
                    batch_branch_db.plan_id = batch_plan.plan_id
                    batch_branch_db.branch_id = store_id
                    batch_branch_db.plan_batch_id = batch_plan.id
                    batch_branch_db.plan_date = batch_plan.plan_date
                    batch_branch_db.partner_id = batch_plan.partner_id
                    batch_branch_db.user_id = batch_plan.user_id
                    batch_branch_db.process_status = 'INIT'
                    batch_branch_db.plan_type = plan_type
                    batch_branch_db.method = method
                    db_session.add(batch_branch_db)
                    batch_branches.append(dict(plan_batch_id=batch_plan.id, branch_id=store_id,
                                               branch_batch_id=batch_branch_db.id, method=method,
                                               plan_type=plan_type, partner_id=partner_id, user_id=user_id
                                               ))
            db_session.commit()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()
        if len(batch_branches) > 0:
            for batch_branch in batch_branches:
                print('batch_branch.{}'.format(batch_branch))
                # self.hand_out_branch_plan_type(**batch_branch)
                mq_producer.publish('boh_order', MessageTopic.DOC_PLAN_GENERATE_TOPIC, batch_branch)
        return True

    #######################轮询补偿#######################
    # batch有数据库唯一性限制
    def get_init_plan_batch(self, partner_id, loop_interval):
        date_time = datetime.utcnow() - timedelta(minutes=loop_interval)
        db_session = session_maker()
        try:
            init_plan_batch_objs = db_session.query(DocBatchPlanDB).filter(DocBatchPlanDB.created_at <= date_time,
                                                                           DocBatchPlanDB.process_status.in_(
                                                                               ['INIT', 'PROCESSING']),
                                                                           DocBatchPlanDB.partner_id == partner_id).all()
            return init_plan_batch_objs
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def update_init_and_fail_branch_batch_retry_count(self, id):
        db_session = session_maker()
        try:
            q = db_session.query(DocBatchPlanBranchDB).filter(
                DocBatchPlanBranchDB.id == id)
            batch_branch_db = q.with_lockmode("update").first()
            if batch_branch_db:
                batch_branch_db.retry_count = batch_branch_db.retry_count + 1 if batch_branch_db.retry_count else 1
                batch_branch_db.updated_at = datetime.utcnow()
                db_session.commit()
            return batch_branch_db
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    # branch_batch执行无数据库唯一性限制，不能选PROCESSING，'INIT', 状态的
    def get_init_and_fail_branch_batch(self, partner_id, loop_interval, retry_count):
        date_time = datetime.utcnow() - timedelta(minutes=loop_interval)
        db_session = session_maker()
        try:
            init_and_fail_branch_batch_objs = db_session.query(DocBatchPlanBranchDB).filter(
                DocBatchPlanBranchDB.updated_at <= date_time,
                DocBatchPlanBranchDB.process_status.in_(['FAIL', 'INIT']),
                or_(DocBatchPlanBranchDB.retry_count <= retry_count,
                    DocBatchPlanBranchDB.retry_count == None),
                DocBatchPlanBranchDB.partner_id == partner_id).all()
            return init_and_fail_branch_batch_objs
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def get_processing_branch_batch(self, partner_id, loop_interval):
        date_time = datetime.utcnow() - timedelta(minutes=loop_interval)
        db_session = session_maker()
        try:
            init_and_fail_branch_batch_objs = db_session.query(DocBatchPlanBranchDB).filter(
                DocBatchPlanBranchDB.updated_at <= date_time,
                DocBatchPlanBranchDB.process_status == "PROCESSING",
                DocBatchPlanBranchDB.partner_id == partner_id).all()
            return init_and_fail_branch_batch_objs
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def update_branch_batch_status(self, partner_id, batch_ids, status):
        db_session = session_maker()
        try:
            processing_branch_batch_objs = db_session.query(DocBatchPlanBranchDB).filter(
                DocBatchPlanBranchDB.id.in_(batch_ids),
                DocBatchPlanBranchDB.partner_id == partner_id).with_lockmode("update").all()
            if processing_branch_batch_objs:
                for obj in processing_branch_batch_objs:
                    obj.process_status = status
                    obj.updated_at = datetime.utcnow()
                    db_session.add(obj)
                db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    """轮询补偿
    业务生单逻辑，不同的plan_type不同的处理逻辑 
        {'plan_batch_id': 4248384430349484032,
         'branch_id': 4217527473673338881,
         'branch_batch_id': 4248384431330951168,
         'method': 'M',
         'plan_type': 'ORDER',
         'partner_id': 2, 'user_id': 4201196624908648450
         }
    """

    # 分发plan_type
    def hand_out_branch_plan_type(self, branch_batch_id, plan_batch_id, branch_id, method, plan_type, partner_id,
                                  user_id):
        if plan_type == 'ORDER':
            self.generate_order(branch_batch_id, plan_batch_id, branch_id, method, plan_type, partner_id,
                                user_id)
        elif plan_type == 'ADJUST':
            self.generate_adjust(branch_batch_id, plan_batch_id, branch_id, method, plan_type, partner_id,
                                 user_id)
        elif plan_type == 'STOCKTAKE':
            self.generate_stocktake(branch_batch_id, plan_batch_id, branch_id, method, plan_type, partner_id,
                                    user_id)
        return True

    #####################################################
    # ORDER
    # 处理plan_type为订货类型的逻辑 ########################
    def generate_order(self, branch_batch_id, plan_batch_id, branch_id, method, plan_type, partner_id,
                       user_id):
        """
        根据门店批次生成门店订货单
        :param branch_batch_id: 门店批次id,如果存在这个id，则忽略以下两个id
        :param plan_batch_id: 计划批次id
        :param branch_id: 门店id
        :param method
        :param plan_type
        :param partner_id
        :param user_id
        :return:
        """
        # 只处理订货
        if plan_type != 'ORDER':
            return True
        # 锁记录 ORDER
        branch_batch = self.doc_plan_repo.lock_branch_batch_process_status(branch_batch_id, plan_batch_id,
                                                                           branch_id, plan_type,
                                                                           ['INIT', 'FAIL'], 'PROCESSING',
                                                                           partner_id=partner_id)
        if not branch_batch:
            return True
        plan, products, categories, _, _ = self.doc_plan_repo.get_plan_by_id(branch_batch.plan_id,
                                                                             partner_id=partner_id,
                                                                             include_category=True,
                                                                             include_product=True)
        if not plan:
            # 无法找到计划则任务失败，不重新执行
            self.doc_plan_repo.lock_branch_batch_process_status(branch_batch_id, plan_batch_id, branch_id, plan_type,
                                                                'PROCESSING',
                                                                'FAIL',
                                                                reason='Invalid plan_id:%s' % branch_batch.plan_id,
                                                                partner_id=partner_id)
            return True
        demand = []
        demand_products = []
        config_product_ids = []
        db_session = session_maker()
        try:
            if plan.get('before'):
                plan_date = branch_batch.plan_date + timedelta(days=plan.get('before'))
            else:
                plan_date = branch_batch.plan_date
            # 获取计划里的商品
            # 全部商品
            if plan.get('product_method') == "ALL":
                product_info = metadata_service.get_product_list(filters={"status": "ENABLED"}, return_fields="id",
                                                                 partner_id=partner_id, user_id=user_id).get('rows',
                                                                                                             [])
                config_product_ids = [convert_to_int(p.get('id')) for p in product_info]
            else:
                # 按类别从主档捞出商品
                if len(categories) > 0:
                    category_ids = [str(category['category_id']) for category in categories if
                                    category.get('category_id')]
                    if len(category_ids) > 0:
                        # 获取在这些类别下面的商品
                        products_raw = []
                        relation_filters = {'product_category': category_ids}
                        products_raw_ret = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                             return_fields="id",
                                                                             partner_id=partner_id, user_id=user_id,
                                                                             )
                        if products_raw_ret:
                            products_raw = products_raw_ret['rows']
                        if isinstance(products_raw, list):
                            for p in products_raw:
                                if p['id'] not in config_product_ids:
                                    config_product_ids.append(int(p['id']))
                # 直接获取配置表里面的商品
                if len(products) > 0:
                    for product in products:
                        if product['product_id'] not in config_product_ids:
                            config_product_ids.append(product['product_id'])
            # 将获取的配置商品与属性区域里面的有效商品进行匹配
            # logger.info("len(products): {} - config_product_ids: {}".format(len(products), config_product_ids))

            if len(config_product_ids) < 0:
                return True
            # username = None
            username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
            store_info = metadata_service.get_store(store_id=branch_id, partner_id=partner_id, user_id=user_id)
            # 取单据冗余字段
            extend = plan.get('extend')
            arrival_date_interval = None
            arrival_date = None
            if extend:
                extend_list = json.loads(extend)
                for ex in extend_list:
                    if ex.get('arrival_date'):
                        arrival_date_interval = int(ex['arrival_date'])
            if arrival_date_interval:
                arrival_date = plan_date + timedelta(days=arrival_date_interval)
            supply_demand = dict(
                id=get_guid(),
                # send_type=send_type,
                bus_type=plan.get('sub_type'),
                batch_id=plan['id'],
                code=Supply_doc_code.get_code_by_type('STORE_DO', partner_id, None),
                store_secondary_id=store_info.get('code'),
                store_type=store_info.get('type'),
                receive_by=branch_id,
                receive_name=store_info.get('name'),
                demand_date=plan_date,
                partner_id=partner_id,
                type='SD',
                created_by=user_id,
                updated_by=user_id,
                created_name=username,
                updated_name=username,
                status='INITED',
                process_status='INITED',
                is_plan=True,
                arrival_date=arrival_date
            )
            demand.append(supply_demand)
            order_products = []
            # saas优化现做bom商品不可参与订货
            # rows = metadata_service.get_product_list(
            #     ids=config_product_ids,
            #     user_id=user_id,
            #     partner_id=partner_id
            # ).get('rows', [])
            # bom_product_ids = []
            # for r in rows:
            #     if r.get("bom_type") == "MANUFACTURE":
            #         bom_product_ids.append(int(r.get('id', 0)))
            #         if int(r.get('id', 0)) in config_product_ids:
            #             config_product_ids.remove(int(r.get('id', 0)))

            # if len(bom_product_ids) > 0:
            #     tea_product_ids = []
            #     for product_id in bom_product_ids:
            #         product_dict = {}
            #         product_dict['product_id'] = product_id
            #         product_dict['quantity'] = 1
            #         tea_product_ids.append(product_dict)
            #     bom_req = dict(
            #         request_id=branch_batch_id, store_id=branch_id,
            #         operation_date=str(plan_date)[:10], product_ids=tea_product_ids,
            #         biz_no=str(branch_batch_id), user_id=user_id, partner_id=partner_id
            #     )
            #     ret = Bom_service.inform_sold_product_record_batch(
            #         **bom_req
            #     )
            #     if ret.get('response'):
            #         r_s = ret.get('response', [])
            #         for r in r_s:
            #             boms = r.get('bom', [])
            #             if len(boms) > 0:
            #                 for bom in boms:
            #                     if int(bom.get('product_id')) not in config_product_ids:
            #                         config_product_ids.append(int(bom.get('product_id')))

            # 原料订货商品
            product_filters = {"status__eq": "ENABLED",
                               "bom_type__neq": "MANUFACTURE",
                               "allow_order": True}
            # logger.info("config_product_ids: {}".format(config_product_ids))
            rows = metadata_service.get_list_valid_product_for_distr_by_id(branch_id,
                                                                           product_ids=config_product_ids,
                                                                           include_product_fields='name,code,model_name,'
                                                                                                  'model_name,model_code,'
                                                                                                  'storage_type,category,'
                                                                                                  'product_type,bom_type,'
                                                                                                  'sale_type,distr_type',
                                                                           order_date=plan_date,
                                                                           product_filters=product_filters,
                                                                           sort='updated',
                                                                           order='desc',
                                                                           partner_id=partner_id,
                                                                           user_id=user_id).get('rows', [])
            logger.info("get_list_valid_product_for_distr_by_id rows: {}".format(rows))
            category_ids = [int(r.get('category')) for r in rows if r.get('category')]
            cate_dict = dict()
            category_list = metadata_service.get_product_category_list(ids=category_ids,
                                                                       return_fields="id,code,name",
                                                                       partner_id=partner_id,
                                                                       user_id=user_id).get('rows', [])
            for c in category_list:
                cate_dict[convert_to_int(c.get('id'))] = c

            product_dict = dict()
            product_list = metadata_service.get_product_list(ids=config_product_ids,
                                                             return_fields="id,code,name,status,allow_order",
                                                             filters={"status":"ENABLED","allow_order":True},
                                                             partner_id=partner_id,
                                                             user_id=user_id).get('rows', [])
            for c in product_list:
                product_dict[convert_to_int(c.get('id'))] = c
            for i in rows:
                # units = i.get('units')
                # unit = {}
                # accounting_unit = {}
                # if units:
                #     for u in units:
                #         if u.get('order') and u.get('order') == True:
                #             unit = u
                #         if u.get('default') and u.get('default') == True:
                #             accounting_unit = u
                # if not unit:
                #     continue
                # 过滤掉bom商品
                if i.get('bom_type') == "MANUFACTURE":
                    continue
                category_id = int(i.get("product_category_id")) if i.get("product_category_id") else 0
                category_name = None
                if isinstance(cate_dict.get(category_id), dict):
                    category_name = cate_dict.get(category_id).get('name')
                product_info = product_dict.get(int(i.get("product_id")), {})
                if not product_info:
                    continue


                rule_extends = dict(
                        plan_arrive_date_text=i.get("plan_arrive_date_text",""),
                        next_plan_arrive_date_text=i.get("next_plan_arrive_date_text",""),
                        safety_qty=i.get("safety_qty",""),
                    )

                temp_product = {
                    'increment_quantity': float(i.get("incr_qty", 0)) if i.get("incr_qty") else 0.0,
                    'max_quantity': float(i.get("max_qty", 0)) if i.get("max_qty") else 0.0,
                    'min_quantity': float(i.get("min_qty", 0)) if i.get("min_qty") else 0.0,
                    'product_category_id': category_id,
                    'product_category_name': category_name,
                    'product_code': product_info.get("code"),
                    'product_id': int(i.get("product_id")) if i.get("product_id") else 0,
                    'product_name': product_info.get("name"),
                    'spec': i.get("spec", "无"),
                    'storage_type': i.get("storage_type"),
                    'distribution_circle': i.get("circle_type"),
                    'arrival_days': arrival_date_interval if arrival_date_interval != None else i.get(
                        "arrival_day", 0),
                    'sale_type': i.get("sale_type"),
                    'product_type': i.get("product_type"),
                    'distribution_center_id': int(i.get("distribution_center_id")) if i.get(
                        "distribution_center_id") else 0,
                    'vendor_id': int(i.get("vendor_id")) if i.get("vendor_id") else 0,
                    # 核算单位
                    'accounting_unit_id': int(i.get("id")) if i.get("id") else 0,
                    'accounting_unit_name': i.get('name', "未设置"),
                    'accounting_unit_spec': i.get("code", "无"),
                    # 订货单位
                    'unit_name': i.get('order_unit_name', "未设置"),
                    'unit_spec': i.get("order_unit_code", "无"),
                    'unit_rate': i.get("order_unit_rate", 1),
                    'unit_id': int(i.get("order_unit_id")) if i.get("order_unit_id") else 0,
                    'cycle_coef': i.get("cycle_coef"),
                    "rule_extends": json.dumps(rule_extends, ensure_ascii=False),
                    "safety_qty" :i.get("safety_qty", ""),
                }
                if i.get("logistic_mode") == 'NMD' or i.get("logistic_mode") == 'PAD':
                    # 配送区域的商品
                    temp_product['distribute_by'] = int(i.get("distribution_center_id")) if i.get(
                        "distribution_center_id") else 0
                    temp_product['distribution_type'] = i.get("logistic_mode")
                # 优先采购区域
                if i.get("logistic_mode") == 'PUR':
                    # 采购区域的商品
                    temp_product['distribute_by'] = int(i.get("distribution_center_id")) if i.get("distribution_center_id") else 0
                    temp_product['distribution_type'] = i.get("logistic_mode")
                    temp_product['purchase_price'] = float(i.get("purchase_price", 0))
                    temp_product['purchase_tax'] = float(i.get('purchase_tax', 0))
                # print('*****', temp_product, distr_type)
                # 大货走配送，冷链走采购
                order_products.append(temp_product)
            # 成品订货商品
            # 门市订货新零售成品当成原料
            store_type = supply_demand['store_type']
            calc_by_safety=plan.get('calc_by_safety_qty',False)
            product_inventory_dict={}
            if order_products:
                if calc_by_safety:
                    # 查询库存
                    product_inventory_dict = inventory_service.query_realtime_inventory_by_branch_id(
                        branch_id=int(branch_id),
                        partner_id=partner_id,
                        user_id=user_id,
                        product_ids=config_product_ids
                    )
                    logger.info('realtime_store_inventory_list：{}'.format(product_inventory_dict))
                s_products = {}
                # 调用Supply接口获取建议订货量
                # need_demand_suggest_product_ids = []
                # valid_sale_types = ['NEW-RETAIL-NORMAL', 'NEW-RETAIL-SHORT-TERM']
                # for i in order_products:
                #     if i.get('product_type') == "FINISHED" and i.get('sale_type', '') in valid_sale_types:
                #         p_id = int(i.get('product_id', 0))
                #         if p_id:
                #             need_demand_suggest_product_ids.append(p_id)
                # demand_day = self.get_timestamp(plan_date)
                # s_ret = supply_service.get_product_sale_forecast_by_store_id(store_id=branch_id,
                #                                                              demand_day=demand_day,
                #                                                              product_ids=need_demand_suggest_product_ids,
                #                                                              partner_id=partner_id,
                #                                                              user_id=user_id)

                # if s_ret:
                #     s_products = s_ret.get('map_field')
                # 两种建议订货量合并
                for product in order_products:
                    # 获取建议订货量
                    suggest_quantity = float(
                        s_products.get(product.get('product_id'))) if s_products.get(
                        product.get('product_id')) else 0
                    if product.get('cycle_coef'):
                        cycle_extends = {'cycle_coef': product.get('cycle_coef')}
                    else:
                        cycle_extends = {}
                    # 获取订货量
                    qty =0.0
                    if calc_by_safety:
                        # 计算订货数量
                        inv_qty=float(product_inventory_dict.get(int(product.get('product_id',0))))/ float(product.get('unit_rate', 1))
                        safety_qty = float(product.get('safety_qty', 0))
                        max_qty = float(product.get('max_quantity', 0))
                        min_qty = float(product.get('min_quantity', 0))
                        increment_quantity= float(product.get('increment_quantity', 0))
                        inv_qty = inv_qty if inv_qty else 0.0
                        qty = safety_qty-inv_qty
                        if qty < 0:
                            qty = 0.0
                        else:
                            qty = min(max_qty,qty)
                            qty = max(min_qty, qty)
                            if (qty-min_qty) % increment_quantity != 0:
                                qty = min_qty + math.ceil((qty - min_qty) / increment_quantity) * increment_quantity


                    demand_product = dict(
                        demand_id=supply_demand['id'],
                        partner_id=partner_id,
                        product_id=product.get('product_id'),
                        product_code=product.get('product_code'),
                        product_name=product.get('product_name'),
                        product_category_id=product.get('product_category_id'),
                        product_category_name=product.get('product_category_name'),
                        increment_quantity=product.get('increment_quantity'),
                        max_quantity=product.get('max_quantity'),
                        min_quantity=product.get('min_quantity'),
                        arrival_days=product.get('arrival_days'),
                        distribution_circle=product.get('distribution_circle'),
                        sale_type=product.get('sale_type'),
                        product_type=product.get('product_type'),
                        unit_id=product.get('unit_id'),
                        unit_spec=product.get('unit_spec'),
                        unit_name=product.get('unit_name'),
                        unit_rate=product.get('unit_rate'),
                        accounting_unit_id=product.get('accounting_unit_id'),
                        accounting_unit_spec=product.get('accounting_unit_spec'),
                        accounting_unit_name=product.get('accounting_unit_name'),
                        spec=product.get('spec'),
                        finished_quantity=0.0,
                        tea_quantity=0.0,
                        bread_quantity=0.0,
                        raw_quantity=0.0,
                        suggest_quantity=suggest_quantity,
                        storage_type=product.get('storage_type'),
                        distribute_by=product.get('distribute_by'),
                        distribution_type=product.get('distribution_type'),
                        purchase_price=product.get('purchase_price'),
                        purchase_tax=product.get('purchase_tax'),
                        cycle_extends=str(cycle_extends),
                        rule_extends= product.get('rule_extends', '{}'),
                        quantity=qty
                    )
                    if product.get('product_type') == "FINISHED":
                        demand_product['finished_quantity'] = 0.0
                    # 普通原料
                    if demand_product.get('product_type') != "FINISHED":
                        demand_product['raw_quantity'] = suggest_quantity
                    demand_products.append(demand_product)
            db_session.bulk_insert_mappings(Supply_demand, demand)
            db_session.bulk_insert_mappings(Supply_demand_product, demand_products)
            db_session.commit()
        except Exception as e:
            self.doc_plan_repo.lock_branch_batch_process_status(branch_batch_id, plan_batch_id, branch_id, plan_type,
                                                                'PROCESSING', 'FAIL', partner_id=partner_id)
            raise e
        self.doc_plan_repo.lock_branch_batch_process_status(branch_batch_id, plan_batch_id, branch_id, plan_type,
                                                            'PROCESSING', 'SUCCESS', partner_id=partner_id)
        # 清理订货单待办缓存
        mq_producer.publish(topic_group="boh_order",
                            topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                            message=dict(partner_id=partner_id, store_id=branch_id,
                                         doc_type="demand"))
        return None

    # 处理plan_type为报废类型的逻辑 ########################
    def generate_adjust(self, branch_batch_id, plan_batch_id, branch_id, method, plan_type, partner_id,
                        user_id):
        # 只处理报废
        if plan_type != 'ADJUST':
            return True
        # 一家门店一天只允许一条自动单据
        start = datetime.utcnow()
        end = datetime.utcnow() + timedelta(days=1)
        start_date = datetime(int(start.year), int(start.month), int(start.day))
        end_date = datetime(int(end.year), int(end.month), int(end.day))
        has_adjust_obj = adjust_repository.have_branch_oneday_adjust(branch_id, start_date, end_date,
                                                                     partner_id)
        if has_adjust_obj:
            return None
        # 锁记录
        branch_batch = self.doc_plan_repo.lock_branch_batch_process_status(branch_batch_id, plan_batch_id,
                                                                           branch_id, plan_type,
                                                                           ['INIT', 'FAIL'], 'PROCESSING',
                                                                           partner_id=partner_id)
        # print('branch_batch', branch_batch)
        if not branch_batch:
            return True
        # 1、先获取门店管理级别审核商品
        plan_of_store_props, products, categories = doc_plan_store_module.get_config_product_of_store(
            branch_batch.plan_id, branch_id, partner_id, status="APPROVE")
        if plan_of_store_props:
            plan, _, _, _, stores = self.doc_plan_repo.get_plan_by_id(branch_batch.plan_id, partner_id=partner_id,
                                                                      include_store=True)
            product_method = plan_of_store_props.get('product_method')
        # 2、获取总部级别商品#########
        else:
            plan, products, categories, _, stores = self.doc_plan_repo.get_plan_by_id(branch_batch.plan_id,
                                                                                      partner_id=partner_id,
                                                                                      include_category=True,
                                                                                      include_product=True,
                                                                                      include_store=True)
            product_method = plan.get('product_method')
        if not plan:
            # 无法找到计划则任务失败，不重新执行
            self.doc_plan_repo.lock_branch_batch_process_status(branch_batch_id, plan_batch_id, branch_id, plan_type,
                                                                'PROCESSING',
                                                                'FAIL',
                                                                reason='Invalid plan_id:%s' % branch_batch.plan_id,
                                                                partner_id=partner_id)
            return True
        db_session = session_maker()
        try:
            if plan.get('before'):
                plan_date = branch_batch.plan_date + timedelta(days=plan.get('before'))
            else:
                plan_date = branch_batch.plan_date
            # username = None
            username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
            region_product_ids = []  # 属性区域获取的商品
            logger.debug('** Gather product from configured attribution region and schedule content **')
            # 获取属性区域配置的可盘点商品
            config_product_ids = []  # 计划配置的商品
            # 获取计划里的商品
            # 全部商品
            if product_method == "ALL":
                product_info = metadata_service.get_product_list(filters={"status": "ENABLED"}, return_fields="id",
                                                                 partner_id=partner_id, user_id=user_id).get('rows',
                                                                                                             [])
                config_product_ids = [convert_to_int(p.get('id')) for p in product_info]
            else:
                # 按类别从主档捞出商品
                if len(categories) > 0:
                    category_ids = [str(category['category_id']) for category in categories if
                                    category.get('category_id')]
                    if len(category_ids) > 0:
                        # 获取在这些类别下面的商品
                        products_raw = []
                        relation_filters = {'product_category': category_ids}
                        products_raw_ret = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                             return_fields="id",
                                                                             partner_id=partner_id, user_id=user_id,
                                                                             )
                        if products_raw_ret:
                            products_raw = products_raw_ret['rows']
                        if isinstance(products_raw, list):
                            for p in products_raw:
                                if p['id'] not in config_product_ids:
                                    config_product_ids.append(int(p['id']))
                # 直接获取配置表里面的商品
                if len(products) > 0:
                    for product in products:
                        if product['product_id'] not in config_product_ids:
                            config_product_ids.append(product['product_id'])
            # logger.info('[1]config_product_ids-{}'.format(config_product_ids))
            store_type = 'STORE'
            if stores:
                store_type = stores[0].get("store_type")
            if store_type == "WAREHOUSE":
                # 默认取仓库配置分类商品(不包含bom商品)
                region_product_ids = get_products_by_branch_id(branch_id=branch_id,
                                                               branch_type=store_type,
                                                               partner_id=partner_id,
                                                               user_id=user_id)
                if len(config_product_ids) > 0:
                    # 将配置商品与仓库下配的分类商品进行匹配
                    config_product_ids = [int(product_id) for product_id in config_product_ids]
                    region_product_ids = list(set(region_product_ids) & set(config_product_ids))
            elif store_type == "MACHINING_CENTER":
                # 默认取加工中心配置分类商品(不包含bom商品)
                region_product_ids = get_products_by_branch_id(branch_id=branch_id,
                                                               branch_type=store_type,
                                                               partner_id=partner_id,
                                                               user_id=user_id)
                if len(config_product_ids) > 0:
                    config_product_ids = [int(c_id) for c_id in config_product_ids]
                    region_product_ids = list(set(region_product_ids) & set(config_product_ids))

            else:
                # 将获取的配置商品与属性区域里面的有效商品进行匹配
                if len(config_product_ids) > 0:
                    # 以下做查询的技术上拆分，防止一次查询太多商品
                    product_group_list = []
                    product_group = []
                    product_count = 0
                    if len(config_product_ids) <= 200:
                        product_group_list.append(config_product_ids)
                    else:
                        for product_id in config_product_ids:
                            if product_count >= 200:
                                product_group_list.append(copy.copy(product_group))
                                del product_group[:]
                                product_count = 0
                            product_count += 1
                            product_group.append(product_id)
                        product_group_list.append(copy.copy(product_group))
                    for g in product_group_list:
                        products_of_store = []
                        filters = {'allow_adjust': True}
                        list_store_product_ret = metadata_service.list_region_product_by_store(
                            store_id=branch_id,
                            product_ids=g,
                            partner_id=partner_id,
                            filters=filters,
                            product_filters={"status": "ENABLED"},
                            user_id=user_id,
                            return_fields="allow_adjust",
                            include_product_fields='id',
                            region="ATTRIBUTE_REGION"
                        )
                        if list_store_product_ret:
                            products_of_store = list_store_product_ret['rows']
                        # logger.info('[1]products_of_store-{}'.format(products_of_store))
                        if isinstance(products_of_store, list):
                            for p in products_of_store:
                                if isinstance(p, dict) \
                                        and 'product_id' in p \
                                        and p['product_id'] \
                                        and p['product_id'] not in region_product_ids:
                                    region_product_ids.append(convert_to_int(p['product_id']))
                else:
                    # 获取属性区域的商品
                    products_of_store = []
                    filters = {'allow_adjust': True}
                    list_store_product_ret = metadata_service.list_region_product_by_store(
                        store_id=branch_id,
                        partner_id=partner_id,
                        filters=filters,
                        product_filters={"status": "ENABLED"},
                        user_id=user_id,
                        return_fields="allow_adjust",
                        include_product_fields='id',
                        region="ATTRIBUTE_REGION"
                    )
                    if list_store_product_ret:
                        products_of_store = list_store_product_ret['rows']
                    if isinstance(products_of_store, list):
                        for p in products_of_store:
                            if isinstance(p, dict) \
                                    and 'product_id' in p \
                                    and p['product_id'] \
                                    and p['product_id'] not in region_product_ids:
                                region_product_ids.append(convert_to_int(p['product_id']))
            adjust_doc = dict(
                id=get_guid(),
                branch_batch_id=branch_batch_id,
                schedule_id=plan['id'],
                code=Supply_doc_code.get_code_by_type(
                    'S_INV_AD', branch_batch.partner_id, None),
                reason_type='-',  # 乐乐茶计划生成报废单默认不给原因
                adjust_store=branch_id,
                status='INITED',
                process_status='INITED',
                created_by=user_id,
                # created_at=datetime.now(LOCAL_TZ),
                partner_id=partner_id,
                adjust_date=plan_date,
                branch_type=store_type,
                source="PLAN_CREATED"
            )
            adjust_detail = dict(
                adjust_id=adjust_doc['id'],
                branch_batch_id=branch_batch_id,
                adjust_store=branch_id,
                schedule_id=plan['id'],
                schedule_code=plan.get('code'),
                schedule_name=plan.get('name'),
                reason_type=adjust_doc['reason_type'],
                code=adjust_doc['code'],
                adjust_date=adjust_doc['adjust_date'],
                created_by=user_id,
                created_name=username,
                # created_at=datetime.now(LOCAL_TZ),
                status='INITED',
                process_status='INITED',
                updated_by=user_id,
                updated_name=username,
                partner_id=partner_id,
                branch_type=store_type,
                source="PLAN_CREATED"
            )
            valid_product_ids = list(set(region_product_ids))
            # 拿到最终商品配置，进行仓位配置
            position_product_map, final_product_ids = doc_plan_position_module.get_plan_position_product_datas_for_create_doc(
                partner_id, user_id, plan.get('id'), branch_id, store_type, valid_product_ids)
            logger.info(
                "final_product_ids-{} \n position_product_map: -{}".format(final_product_ids, position_product_map))

            final_products = []
            # 开始从主档获取商品的补充信息
            unit_info_ret = metadata_service.get_unit_list(return_fields='id,name,code', partner_id=partner_id,
                                                           user_id=user_id).get('rows', [])
            unit_info_dict = {}
            if len(unit_info_ret) > 0:
                for u in unit_info_ret:
                    unit_info_dict[int(u['id'])] = [u.get('name'), u.get('code')]
            filters = {"status": "ENABLED", }
            main_products_ret = metadata_service.get_product_list(ids=final_product_ids, include_units=True,
                                                                  filters=filters,
                                                                  return_fields='id,code,name',
                                                                  partner_id=partner_id, user_id=user_id)
            main_products = []
            if main_products_ret:
                main_products = main_products_ret['rows']
            final_product_info_dict = {}
            for main_p in main_products:
                units = main_p['units'] if 'units' in main_p else []
                product_id = convert_to_int(main_p['id']) if 'id' in main_p else None
                # 获取配方单位和核算单位
                unit_id = 0
                accounting_unit_id = 0
                if len(units) > 0:
                    # saas 报废统一默认取核算单位
                    for unit in units:
                        if unit.get('default'):
                            accounting_unit_id = convert_to_int(unit.get('id'))
                            unit_id = convert_to_int(unit.get('id'))
                    # if store_type == "WAREHOUSE":
                    #     # 仓库报废取订货单位
                    #     for unit in units:
                    #         if unit.get('default'):
                    #             accounting_unit_id = convert_to_int(unit['id'])
                    #         if unit.get('order'):
                    #             unit_id = convert_to_int(unit['id'])
                # 商品必须要有核算单位和bom单位
                if unit_id and accounting_unit_id:
                    main_p['unit_id'] = unit_id
                    main_p['accounting_unit_id'] = accounting_unit_id
                    final_product_info_dict[product_id] = main_p
            if position_product_map:
                for position_id, product_list in position_product_map.items():
                    for product_id in product_list:
                        p = final_product_info_dict.get(product_id)
                        if not p:
                            continue
                        unit_id = p['unit_id']
                        accounting_unit_id = p['accounting_unit_id']
                        unit_info = unit_info_dict.get(unit_id)
                        accounting_unit_info = unit_info_dict.get(accounting_unit_id)
                        final_product = dict(
                            id=get_guid(),
                            adjust_id=adjust_doc['id'],
                            product_id=product_id,
                            product_code=p.get('code'),
                            product_name=p.get('name'),
                            unit_id=unit_id,
                            unit_name=unit_info[0] if unit_info else None,
                            unit_spec=unit_info[1] if unit_info else None,
                            accounting_unit_id=accounting_unit_id,
                            accounting_unit_name=accounting_unit_info[0] if unit_info else None,
                            accounting_unit_spec=accounting_unit_info[1] if unit_info else None,
                            adjust_store=branch_id,
                            adjust_date=adjust_doc['adjust_date'],
                            reason_type=adjust_doc['reason_type'],
                            created_by=user_id,
                            # created_at=datetime.now(LOCAL_TZ),
                            updated_by=user_id,
                            user_id=user_id,
                            partner_id=partner_id,
                            position_id=position_id,
                        )
                        final_products.append(final_product)
            adjust_detail['adjust_order_number'] = adjust_detail['code']
            adjust_log_db = AdjustLogDB()
            adjust_log_db.id = get_guid()
            adjust_log_db.adjust_id = adjust_doc['id']
            adjust_log_db.adjust_status = 'INITED'
            adjust_log_db.created = datetime.utcnow()
            adjust_log_db.partner_id = partner_id
            adjust_log_db.reason = adjust_detail['source']
            adjust_log_db.created_by = user_id
            db_session.add(adjust_log_db)
            db_session.bulk_insert_mappings(AdjustDB, [adjust_doc])
            db_session.bulk_insert_mappings(AdjustDetailsDB, [adjust_detail])
            db_session.bulk_insert_mappings(AdjustProductDB, final_products)
            db_session.commit()
        except Exception as e:
            self.doc_plan_repo.lock_branch_batch_process_status(branch_batch_id, plan_batch_id, branch_id, plan_type,
                                                                'PROCESSING', 'FAIL', partner_id=partner_id)
            raise e
        self.doc_plan_repo.lock_branch_batch_process_status(branch_batch_id, plan_batch_id, branch_id, plan_type,
                                                            'PROCESSING', 'SUCCESS', partner_id=partner_id)
        # 清理待办缓存
        mq_producer.publish(topic_group="boh_order",
                            topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                            message=dict(partner_id=partner_id, store_id=branch_id,
                                         doc_type="adjust"))
        return None

    # 处理plan_type为盘点类型的逻辑 ########################
    def generate_stocktake(self, branch_batch_id, plan_batch_id, branch_id, method, plan_type, partner_id,
                           user_id):
        """
        根据门店批次生成门店盘点单
        :param branch_batch_id: 门店批次id,如果存在这个id，则忽略以下两个id
        :param plan_batch_id: 计划批次id
        :param branch_id: 门店id
        :param method:
        :param plan_type: 计划类型
        :param partner_id
        :param user_id
        :return:
        """
        # 只处理盘点
        if plan_type != 'STOCKTAKE':
            return True
        # 锁记录
        branch_batch = self.doc_plan_repo.lock_branch_batch_process_status(branch_batch_id, plan_batch_id,
                                                                           branch_id, plan_type,
                                                                           ['INIT', 'FAIL'], 'PROCESSING',
                                                                           partner_id=partner_id)
        # print('branch_batch', branch_batch)
        if not branch_batch:
            return True
        # 1、先获取门店管理级别审核商品
        plan_of_store_props, products, categories = doc_plan_store_module.get_config_product_of_store(
            branch_batch.plan_id, branch_id, partner_id, status="APPROVE")
        if plan_of_store_props:
            plan, _, _, _, stores = self.doc_plan_repo.get_plan_by_id(branch_batch.plan_id, partner_id=partner_id,
                                                                      include_store=True)
            product_method = plan_of_store_props.get('product_method')
        # 2、获取总部级别商品
        else:
            plan, products, categories, _, stores = self.doc_plan_repo.get_plan_by_id(branch_batch.plan_id,
                                                                                      partner_id=partner_id,
                                                                                      include_category=True,
                                                                                      include_product=True,
                                                                                      include_store=True)
            product_method = plan.get('product_method')

        #########################
        if not plan:
            # 无法找到计划则任务失败，不重新执行
            self.doc_plan_repo.lock_branch_batch_process_status(branch_batch_id, plan_batch_id, branch_id, plan_type,
                                                                'PROCESSING',
                                                                'FAIL',
                                                                reason='Invalid plan_id:%s' % branch_batch.plan_id,
                                                                partner_id=partner_id)
            return True
        # 一家门店一天只允许一条自动单据，并按月，周，日的覆盖原则
        stocktake_circle = ['D', 'DW', 'DWM'] if plan.get('method') == 'D' \
            else ['W', 'DW', 'DWM', 'WM'] if plan.get('method') == 'W' \
            else ['M', 'WM', 'DWM'] if plan.get('method') == 'M' \
            else None
        try:
            if plan.get('before'):
                target_date = branch_batch.plan_date + timedelta(days=plan.get('before'))
            else:
                target_date = branch_batch.plan_date
            username = metadata_service.get_username_by_pid_uid(branch_batch.partner_id, branch_batch.user_id)
            store_type = plan.get('store_type')
            if not store_type:
                store_type = stores[0].get("store_type")
            # 查询门店盘点生单业务配置
            limit_open = False
            if store_type == "STORE":
                extra_config = metadata_service.get_business_extra_config(partner_id=partner_id,
                                                                          domain='boh.store.stocktake', user_id=user_id)
                limit_open = extra_config.get('daily_generate_only_one')
            if limit_open is True:
                method = plan.get('method')
                is_continue = True
                invalid_docs = []
                if method in self.ST_DOC_TYPE.keys():
                    # 查询当日是否存在生效的盘点单
                    exist_docs = SupplySTDocDB.list_st_doc(partner_id=partner_id, target_date=target_date,
                                                           branch_ids=[branch_id],
                                                           except_status=["CANCELLED"],
                                                           _type=list(self.ST_DOC_TYPE.keys()))
                    if exist_docs:
                        # 按优先级排序
                        exist_docs = sorted(exist_docs, key=lambda d: self.ST_DOC_TYPE.get(d.type), reverse=True)
                        # first_doc_type = exist_docs[0].type
                        # if self.ST_DOC_TYPE.get(method) > self.ST_DOC_TYPE.get(first_doc_type):
                        #     is_continue = True

                        for inx, doc in enumerate(exist_docs):
                            if doc.status not in ["INITED", "REJECTED"]:
                                is_continue = False
                            else:
                                if inx == 0:
                                    if self.ST_DOC_TYPE.get(method) > self.ST_DOC_TYPE.get(doc.type):
                                        invalid_docs.append(doc.id)
                                        is_continue = True
                                    else:
                                        is_continue = False
                                else:
                                    invalid_docs.append(doc.id)
                    else:
                        is_continue = True
                if len(invalid_docs) > 0:
                    for doc_id in invalid_docs:
                        SupplySTDocDB.lock_stocktake_status(doc_id=doc_id, current_status=["INITED", "REJECTED"],
                                                            status="CANCELLED",
                                                            user_id=user_id, partner_id=partner_id, username=username,
                                                            reason="开启单日生单唯一限制作废")
                    # 清理待办缓存
                    mq_producer.publish(topic_group="boh_order",
                                        topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                        message=dict(partner_id=partner_id, store_id=branch_id,
                                                     doc_type="stocktake"))
                # 当前盘点单优先级不大于已存在盘点单 -- 不再继续生单
                if is_continue is False:
                    self.doc_plan_repo.lock_branch_batch_process_status(branch_batch_id, plan_batch_id, branch_id,
                                                                        plan_type,
                                                                        'PROCESSING', 'SUCCESS',
                                                                        reason="daily generate limit",
                                                                        partner_id=partner_id)
                    return True

            region_product_ids = []  # 属性区域获取的商品
            logger.debug('** Gather product from configured attribution region and schedule content **')
            # 获取属性区域配置的可盘点商品
            config_product_ids = []  # 盘点计划配置的商品
            # 获取计划里的商品
            # 全部商品
            if product_method == "ALL":
                product_info = metadata_service.get_product_list(filters={"status": "ENABLED"}, return_fields="id",
                                                                 partner_id=partner_id, user_id=user_id).get('rows',
                                                                                                             [])
                config_product_ids = [convert_to_int(p.get('id')) for p in product_info]
            else:
                # 按类别从主档捞出商品
                if len(categories) > 0:
                    category_ids = [str(category['category_id']) for category in categories if
                                    category.get('category_id')]
                    if len(category_ids) > 0:
                        # 获取在这些类别下面的商品
                        products_raw = []
                        relation_filters = {'product_category': category_ids}
                        products_raw_ret = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                             return_fields="id",
                                                                             partner_id=partner_id, user_id=user_id,
                                                                             )
                        if products_raw_ret:
                            products_raw = products_raw_ret['rows']
                        if isinstance(products_raw, list):
                            for p in products_raw:
                                if p['id'] not in config_product_ids:
                                    config_product_ids.append(int(p['id']))
                # 直接获取配置表里面的商品
                if len(products) > 0:
                    for product in products:
                        if product['product_id'] not in config_product_ids:
                            config_product_ids.append(product['product_id'])
            # 取单据新增标记是否取实时库存字段include_inventory_product True/False
            extend = plan.get('extend')
            include_inventory_product = False
            if extend:
                extend_list = json.loads(extend)
                for ex in extend_list:
                    if ex.get('include_inventory_product'):
                        include_inventory_product = ex['include_inventory_product']
            logger.info("include_inventory_product: -- {}".format(include_inventory_product))
            # 盘点计划根据库存智能添加商品,
            # 盘点计划增加勾选项“添加实时库存商品”, 自动添加门店或仓库实时库存中有正负库存的商品（不包含成品和半成品）
            # 用于保存库存中查出来的商品(过滤掉成品和半成品)
            inventory_product_ids_filter = []
            # if (plan.get('method') == 'W') or (plan.get('method') == 'M' and plan.get('sub_type') != 'R'):
            if include_inventory_product is True:
                realtime_store_inventory_list = inventory_service.query_realtime_inventory_products_ids_by_branch_id(
                    branch_id=int(branch_id),
                    partner_id=partner_id,
                    user_id=user_id
                )
                logger.info('realtime_store_inventory_list：{}'.format(realtime_store_inventory_list))
                # 过滤掉成品和半成品
                filters = {"bom_type__neq": "MANUFACTURE"}
                rows = metadata_service.get_product_list(ids=realtime_store_inventory_list,
                                                         filters=filters,
                                                         return_fields="id",
                                                         partner_id=partner_id,
                                                         user_id=user_id).get("rows", [])
                no_finished_and_semi_finished = [convert_to_int(row.get("id")) for row in rows] if rows else []
                for product_id in realtime_store_inventory_list:
                    if product_id not in config_product_ids and product_id in no_finished_and_semi_finished:
                        config_product_ids.append(product_id)
                # 库存取出商品和过滤掉成品半成品的商品进行交集得到最终库存拉出的商品
                inventory_product_ids_filter = list(
                    set(realtime_store_inventory_list) & set(no_finished_and_semi_finished))
                logger.info('inventory_product_ids_filter：{}'.format(inventory_product_ids_filter))
            if store_type == "WAREHOUSE":
                # 默认取加工中心配置分类商品(不包含bom商品)
                region_product_ids = get_products_by_branch_id(branch_id=branch_id,
                                                               branch_type=store_type,
                                                               partner_id=partner_id,
                                                               user_id=user_id)
                if len(config_product_ids) > 0:
                    # 将配置商品与仓库下配的分类商品进行匹配
                    config_product_ids = [int(product_id) for product_id in config_product_ids]
                    region_product_ids = list(set(region_product_ids) & set(config_product_ids))
                    # print('WAREHOUSE-region_product_ids', region_product_ids)
            elif store_type == "MACHINING_CENTER":
                # 默认取加工中心配置分类商品(不包含bom商品)
                region_product_ids = get_products_by_branch_id(branch_id=branch_id,
                                                               branch_type=store_type,
                                                               partner_id=partner_id,
                                                               user_id=user_id)
                if len(config_product_ids) > 0:
                    # 将配置商品与加工中心下配的分类商品进行匹配
                    config_product_ids = [int(c_id) for c_id in config_product_ids]
                    region_product_ids = list(set(region_product_ids) & set(config_product_ids))
                    # print('MACHINING_CENTER-region_product_ids', region_product_ids)
            else:
                # 将获取的配置商品与属性区域里面的有效商品进行匹配
                filters = {'allow_stocktake': True}
                # 不定期不校验循环规则
                if plan.get('sub_type') != 'R':
                    filters['cycle_type__in'] = stocktake_circle
                if len(config_product_ids) > 0:
                    # 以下做查询的技术上拆分，防止一次查询太多商品
                    product_group_list = []
                    product_group = []
                    product_count = 0
                    if len(config_product_ids) <= 200:
                        product_group_list.append(config_product_ids)
                    else:
                        for product_id in config_product_ids:
                            if product_count >= 200:
                                product_group_list.append(copy.copy(product_group))
                                del product_group[:]
                                product_count = 0
                            product_count += 1
                            product_group.append(product_id)
                        product_group_list.append(copy.copy(product_group))
                    for g in product_group_list:
                        products_of_store = []
                        product_filters = {
                            "product_type__nin": ["MATERIAL_CONSUME", "LOW_CONSUME"]}
                        product_fields = "product_type,bom_type"
                        products_of_store_ret = metadata_service.list_region_product_by_store(
                            store_id=branch_id,
                            product_ids=g,
                            filters=filters,
                            # product_filters=product_filters,
                            partner_id=partner_id,
                            user_id=user_id,
                            return_fields="allow_stocktake",
                            include_product_fields=product_fields,
                            region="ATTRIBUTE_REGION",
                            check_division=False
                        )
                        if products_of_store_ret:
                            products_of_store = products_of_store_ret['rows']
                        logger.info("products_of_store: {}".format(products_of_store))
                        if isinstance(products_of_store, list):
                            for p in products_of_store:
                                # 默认：配方属性=现做BOM” 不可盘点
                                if p.get('bom_type') == 'MANUFACTURE' or (not p.get('allow_stocktake')):
                                    continue
                                if isinstance(p, dict) \
                                        and 'product_id' in p \
                                        and p['product_id'] \
                                        and p['product_id'] not in region_product_ids:
                                    region_product_ids.append(convert_to_int(p['product_id']))
                else:
                    # 获取属性区域的商品
                    products_of_store = []
                    product_fields = "product_type,bom_type"
                    products_of_store_ret = metadata_service.list_region_product_by_store(
                        store_id=branch_id,
                        filters=filters,
                        # product_filters=product_filters,
                        partner_id=partner_id,
                        user_id=user_id,
                        return_fields="allow_stocktake",
                        include_product_fields=product_fields,
                        region="ATTRIBUTE_REGION",
                        check_division=False
                    )
                    if products_of_store_ret:
                        products_of_store = products_of_store_ret['rows']
                    if isinstance(products_of_store, list):
                        for p in products_of_store:
                            # 商品属性=成品 &&配方属性=现做BOM” 不可盘点
                            if p.get('bom_type') == 'MANUFACTURE' or (not p.get('allow_stocktake')):
                                continue
                            if isinstance(p, dict) \
                                    and 'product_id' in p \
                                    and p['product_id'] \
                                    and p['product_id'] not in region_product_ids:
                                region_product_ids.append(convert_to_int(p['product_id']))
            valid_product_ids = list(set(region_product_ids))
            #  position拿到最终商品配置，进行仓位配置
            position_product_map, final_product_ids = doc_plan_position_module.get_plan_position_product_datas_for_create_doc(
                partner_id, user_id, plan.get('id'), branch_id, store_type, valid_product_ids)
            # print('final_product_ids', final_product_ids, position_product_map)
            # 开始从主档获取商品的补充信息, 现做bom商品不能盘点
            filters = {"bom_type__neq": 'MANUFACTURE'}
            final_product_info_dict = {}
            main_products_ret = None
            if len(final_product_ids) > 0:
                main_products_ret = metadata_service.get_product_list(ids=final_product_ids, include_units=True,
                                                                      filters=filters,
                                                                      return_fields='id,code,name,storage_type,status',
                                                                      partner_id=partner_id, user_id=user_id)
            main_products = []
            unit_ids = []
            # print('main_products_ret', main_products_ret)
            if main_products_ret:
                main_products = main_products_ret['rows']
            check_product_ids = []
            for main_p in main_products:
                if isinstance(main_p, dict):
                    product_id = convert_to_int(main_p['id']) if 'id' in main_p else None
                    if main_p.get('status') == "DISABLED" and product_id not in inventory_product_ids_filter:
                        continue
                    final_product = dict()
                    units = main_p['units'] if 'units' in main_p else []
                    has_unit = False
                    has_default_unit = False
                    if product_id and product_id in final_product_ids and product_id not in check_product_ids:
                        final_product['id'] = product_id
                        check_product_ids.append(product_id)
                        if 'code' in main_p and main_p['code']:
                            final_product['code'] = main_p['code']
                        if 'name' in main_p and main_p['name']:
                            final_product['name'] = main_p['name']
                        if 'storage_type' in main_p and main_p['storage_type']:
                            final_product['storage_type'] = main_p['storage_type']
                        # 获取核算单位和盘点单位
                        if isinstance(units, list) and len(units) > 0:
                            for unit in units:
                                if isinstance(unit, dict) and 'id' in unit and unit['id']:
                                    if not has_default_unit and 'default' in unit and unit['default']:
                                        final_product['default_unit'] = convert_to_int(unit['id'])
                                        has_default_unit = True
                                    if unit.get('default_stocktake'):
                                        final_product['unit'] = convert_to_int(unit['id'])
                                        final_product['unit_rate'] = round(unit.get('rate'), 7) if unit.get(
                                            'rate') else None
                                        has_unit = True
                                    if not has_unit and unit.get('stocktake'):
                                        final_product['unit'] = convert_to_int(unit['id'])
                                        final_product['unit_rate'] = round(unit.get('rate'), 7) if unit.get(
                                            'rate') else None
                                        has_unit = True
                                    # if has_unit and has_default_unit:
                                    #     break
                    # 商品必须要有核算单位和盘点单位
                    if len(final_product) > 0 and has_default_unit and has_unit:
                        final_product_info_dict[product_id] = final_product
                        if str(final_product['unit']) not in unit_ids:
                            unit_ids.append(str(final_product['unit']))
                        if str(final_product['default_unit']) not in unit_ids:
                            unit_ids.append(str(final_product['default_unit']))
            unit_dict = {}
            if len(unit_ids) > 0:
                units_ret = metadata_service.get_unit_list(return_fields='id,name,code',
                                                           partner_id=partner_id,
                                                           user_id=user_id)
                units = []
                if units_ret:
                    units = units_ret['rows']
                if isinstance(units, list):
                    for u in units:
                        if isinstance(u, dict) and 'id' in u and u['id'] in unit_ids:
                            unit_dict[str(u['id'])] = dict()
                            if 'name' in u:
                                unit_dict[str(u['id'])]['name'] = u['name']
                            if 'code' in u:
                                unit_dict[str(u['id'])]['code'] = u['code']
            # print('final_product_info_dict', final_product_info_dict, unit_dict)
            # 加盟门店盘点取商品价格
            product_price_map = dict()
            if store_type == "FRS_STORE":
                product_price_map = get_frs_product_price_map(store_id=branch_batch.branch_id,
                                                              partner_id=partner_id, user_id=user_id)
            db_session = session_maker()
            try:
                stocktake_doc = dict(
                    id=get_guid(),
                    batch_id=branch_batch.batch_id,
                    branch_batch_id=branch_batch.id,
                    branch_id=branch_batch.branch_id,
                    type=plan.get('method'),
                    target_date=target_date,
                    schedule_id=plan.get('id'),
                    status='INITED',
                    process_status='INITED',
                    calculate_inventory=plan.get('calculate_inventory'),
                    partner_id=branch_batch.partner_id,
                    user_id=branch_batch.user_id,
                    created_name=username,
                    updated_name=username,
                    created_by=user_id,
                    updated_by=user_id,
                )
                # 月盘需要8位
                if plan.get('method') == 'M':
                    stocktake_doc['code'] = Stocktake_month_code.get_stocktake_code(partner_id)
                else:
                    stocktake_doc['code'] = Supply_doc_code.get_code_by_type('STORE_ST', branch_batch.partner_id, None)
                stocktake_doc_detail = dict(
                    doc_id=stocktake_doc['id'],
                    batch_id=stocktake_doc['batch_id'],
                    # 盘点属性
                    stocktake_type=plan.get('sub_type') if plan.get('sub_type') else "PLAN",
                    branch_batch_id=stocktake_doc['branch_batch_id'],
                    branch_id=stocktake_doc['branch_id'],
                    code=stocktake_doc['code'],
                    schedule_id=plan.get('id'),
                    schedule_code=plan.get('code'),
                    schedule_name=plan.get('name'),
                    target_date=stocktake_doc['target_date'],
                    type=plan.get('method'),
                    status='INITED',
                    process_status='INITED',
                    branch_type=store_type,
                    calculate_inventory=plan.get('calculate_inventory'),
                    partner_id=branch_batch.partner_id,
                    user_id=branch_batch.user_id,
                    created_name=username,
                    updated_name=username,
                    created_by=user_id,
                    updated_by=user_id,
                    total_amount=0
                )
                stocktake_products = []
                if position_product_map:
                    for position_id, product_list in position_product_map.items():
                        for product_id in product_list:
                            p = final_product_info_dict.get(product_id)
                            if not p:
                                continue
                            stocktake_product = dict(
                                id=get_guid(),
                                doc_id=stocktake_doc['id'],
                                product_id=p['id'],
                                product_code=p.get('code'),
                                product_name=p.get('name'),
                                storage_type=p.get('storage_type'),
                                unit_rate=p.get('unit_rate'),
                                accounting_unit_id=p.get('default_unit'),
                                unit_id=p.get('unit'),
                                is_system=True,
                                partner_id=branch_batch.partner_id,
                                user_id=branch_batch.user_id,
                                created_name=username,
                                updated_name=username,
                                branch_id=stocktake_doc_detail['branch_id'],
                                target_date=stocktake_doc_detail['target_date'],
                                status='INITED',
                                created_by=user_id,
                                updated_by=user_id,
                                position_id=position_id,
                                is_null=True,
                                amount=0,
                                diff_amount=0
                            )
                            product_price = product_price_map.get(product_id)
                            if product_price:
                                account_tax_price = product_price.get('account_tax_price', 0)
                                # unit_rate = convert_to_decimal(p.get('unit_rate', 1))
                                tax_rate = product_price.get('tax_ratio', 0)
                                # tax_price = unit_rate * account_tax_price
                                # cost_price = (account_tax_price / (1 + tax_rate)) * unit_rate
                                cost_price = account_tax_price / (1 + tax_rate)
                                stocktake_product['tax_rate'] = tax_rate
                                stocktake_product['tax_price'] = account_tax_price
                                stocktake_product['cost_price'] = cost_price
                                stocktake_product['sales_price'] = product_price.get('account_sales_price')
                            if str(stocktake_product['unit_id']) in unit_dict:
                                stocktake_product['unit_name'] = \
                                    unit_dict[str(stocktake_product['unit_id'])].get('name', '')
                                stocktake_product['unit_spec'] = \
                                    unit_dict[str(stocktake_product['unit_id'])].get('code')
                            if str(stocktake_product['accounting_unit_id']) in unit_dict:
                                stocktake_product['accounting_unit_name'] = \
                                    unit_dict[str(stocktake_product['accounting_unit_id'])].get('name')
                                stocktake_product['accounting_unit_spec'] = \
                                    unit_dict[str(stocktake_product['accounting_unit_id'])].get('code')
                            stocktake_products.append(stocktake_product)
                doc_log_db = SupplySTDocLogDB()
                doc_log_db.id = get_guid()
                doc_log_db.doc_id = stocktake_doc['id']
                doc_log_db.partner_id = stocktake_doc['partner_id']
                doc_log_db.doc_status = stocktake_doc['status']
                doc_log_db.created_at = datetime.utcnow()
                doc_log_db.created_by = user_id
                doc_log_db.reason = stocktake_doc_detail['stocktake_type']
                db_session.add(doc_log_db)
                db_session.bulk_insert_mappings(SupplySTDocDB, [stocktake_doc])
                db_session.bulk_insert_mappings(SupplySTDocDetailsDB, [stocktake_doc_detail])
                db_session.bulk_insert_mappings(SupplySTProductDB, stocktake_products)
                db_session.commit()
            except Exception as e:
                db_session.rollback()
                raise e
            finally:
                db_session.close()
        except Exception as e:
            self.doc_plan_repo.lock_branch_batch_process_status(branch_batch_id, plan_batch_id, branch_id,
                                                                plan_type,
                                                                'PROCESSING', 'FAIL', partner_id=partner_id)
            raise e
        self.doc_plan_repo.lock_branch_batch_process_status(branch_batch_id, plan_batch_id, branch_id, plan_type,
                                                            'PROCESSING', 'SUCCESS', partner_id=partner_id)
        # 清理待办缓存
        mq_producer.publish(topic_group="boh_order",
                            topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                            message=dict(partner_id=partner_id, store_id=branch_id,
                                         doc_type="stocktake"))
        return None

    ########################################################
    def get_doc_plan_calendar(self, request, partner_id, user_id):
        """运营日历计划查询"""
        res = {}
        store_id = request.store_id
        store_type = request.store_type
        start_date = self.get_datetime(request.start_date)
        end_date = self.get_datetime(request.end_date)
        utc_offset = request.utc_offset
        if not utc_offset:
            utc_offset = self.get_local_utc_offset()
        status = "CONFIRM"  # 只查询已经确认生效的计划
        list_plan = self.doc_plan_repo.get_plan_by_date([store_id], partner_id, status=status,
                                                        store_type=store_type, start_date=start_date,
                                                        end_date=end_date)
        plan_ids = [plan.get("id") for plan in list_plan]
        control_status_list = self.doc_plan_status_repo.get_valid_all(partner_id=partner_id, plan_ids=plan_ids)
        plan_status_map = dict()
        for c in control_status_list:
            row = dict(
                time=c.time,
                time_around=c.time_around,
                start_status=str(c.start_status).split(','),
                end_status=c.end_status,
                doc_filter=c.doc_filter
            )
            if c.plan_id in plan_status_map.keys():
                plan_status_map[c.plan_id].append(row)
            else:
                plan_status_map[c.plan_id] = [row]
        query_date_str_list = self.get_date_range(start_date=start_date + timedelta(hours=utc_offset),
                                                  end_date=end_date + timedelta(hours=utc_offset),
                                                  delta_day=1)
        res['rows'] = []
        for plan in list_plan:
            row = dict(
                plan_id=plan.get('id'),
                plan_type=plan.get('plan_type'),
                code=plan.get('code'),
                name=plan.get('name'),
                plan_date_list=[],  # 实际单据业务日期
                plan_time=self.get_timestamp(plan.get('start')),
                start=self.get_timestamp(plan.get('start')),
                before=plan.get('before')
            )
            c_start = plan.get('start') + timedelta(hours=utc_offset)
            c_start_hour = c_start.hour
            c_start_minute = c_start.minute
            c_start_second = c_start.second
            c_confirm_time = plan.get('updated_at') + timedelta(hours=utc_offset)  # 计划确认时间
            control_status = plan_status_map.get(plan.get('id'), [])
            row['control_status'] = control_status
            # 极其恶心！如果计划状态变更有多条记录，取其中最早的那个时间
            # if control_status:
            #     for s in control_status:
            #         target_date = start.date()
            #         time_around = s.get("time_around")
            #         _time = s.get('time')
            #         if time_around == 'Yesterday':
            #             target_date = target_date - timedelta(days=1)
            #         elif time_around == "Today":
            #             target_date = target_date
            #         elif time_around == 'Tomorrow':
            #             target_date = target_date + timedelta(days=1)
            #         status_time_tmp = datetime.strptime(str(target_date)[:10] + " " + _time, "%Y-%m-%d %H:%M:%S")
            #         if control_status_time:
            #             if status_time_tmp < control_status_time:
            #                 control_status_time = status_time_tmp
            #                 row['time_around'] = time_around
            #         else:
            #             control_status_time = status_time_tmp
            #             row['time_around'] = time_around
            # if control_status_time:
            #     row['end'] = self.get_timestamp(control_status_time)
            interval = plan.get('interval')
            before = plan.get('before')
            method = plan.get('method')
            sub_type = plan.get('sub_type')
            plan_start_date = datetime(year=c_start.year, month=c_start.month, day=c_start.day,
                                       hour=0, minute=0, second=0)
            plan_end_date = plan.get('end') + timedelta(hours=utc_offset)
            if method == "D" and sub_type != 'R':
                day_method = plan.get('day_method')
                if day_method:
                    # 判断起始量
                    plan_start_date = datetime.strptime(day_method[:10], '%Y-%m-%d')
                plan_date_list = self.get_date_range(start_date=plan_start_date,
                                                     end_date=plan.get('end') + timedelta(hours=utc_offset),
                                                     delta_day=interval + 1)
                plan_date_list = list(set(plan_date_list) & set(query_date_str_list))
                row['plan_date_list'] = plan_date_list
            elif method == "W" and sub_type != 'R':
                plan_date_list = []
                # [0,1,2,3,4,5..]
                # if before:
                #     plan_end_date = plan.get('end') + timedelta(hours=utc_offset) + timedelta(days=(int(before)))
                # else:
                #     plan_end_date = plan.get('end') + timedelta(hours=utc_offset)
                plan_date_week_map = self.get_date_week_map(start_date=plan_start_date,
                                                            end_date=plan_end_date)
                week_method = plan.get('week_method')
                if not week_method:
                    continue
                if "-" in week_method:
                    week_method, week_month = week_method.split('-')
                else:
                    week_month = 0
                for date_str, week in plan_date_week_map.items():
                    year, month, day = date_str.split('-') if '-' in date_str else [1, 1, 1]
                    week_month_now = self.get_week_of_month(int(year), int(month), int(day))
                    if week_method[week] == '1' and (int(week_month) == week_month_now or week_month == 0):
                        plan_date_list.append(date_str)
                row['plan_date_list'] = list(set(plan_date_list) & set(query_date_str_list))
            elif method == "M" and sub_type != "R":
                month_method = plan.get('month_method')
                query_date_list = self.get_date_range(start_date=start_date + timedelta(hours=utc_offset),
                                                      end_date=end_date + timedelta(hours=utc_offset),
                                                      delta_day=1, return_date=True)
                if not month_method:
                    continue
                month_method_list = [int(c) for c in month_method.split(',')]
                for target_date in query_date_list:
                    if plan_start_date <= target_date <= plan.get('end') + timedelta(hours=utc_offset):
                        _, month_range = calendar.monthrange(target_date.year, target_date.month)
                        # 月末倒数天数计算，是否是倒数第3，2，1天也满足
                        interval_range = target_date.day - month_range - 1
                        if target_date.day in month_method_list:
                            row['plan_date_list'].append(target_date.strftime('%Y-%m-%d'))
                        elif interval_range in month_method_list:
                            row['plan_date_list'].append(target_date.strftime('%Y-%m-%d'))
            final_date_list = []
            for pd in row['plan_date_list']:
                pdd = datetime.strptime(pd, '%Y-%m-%d')
                if before:
                    pdd = pdd - timedelta(days=abs(int(before)))
                if pdd.replace(hour=c_start_hour, minute=c_start_minute, second=c_start_second) > c_confirm_time:
                    final_date_list.append(pd)
            row['plan_date_list'] = final_date_list
            res['rows'].append(row)
        return res


doc_plan_module = DocPlanModule()
