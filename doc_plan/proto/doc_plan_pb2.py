# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: doc_plan.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from metadata import product_pb2 as metadata_dot_product__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='doc_plan.proto',
  package='doc_plan',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x0e\x64oc_plan.proto\x12\x08\x64oc_plan\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x16metadata/product.proto\"\x06\n\x04Null\"\x13\n\x04Pong\x12\x0b\n\x03msg\x18\x01 \x01(\t\"\xb2\x06\n\x11\x43reatePlanRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x11\n\tplan_type\x18\x03 \x01(\t\x12\x0e\n\x06\x62\x65\x66ore\x18\x04 \x01(\r\x12\x1b\n\x13\x63\x61lculate_inventory\x18\x05 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x0c\n\x04name\x18\x07 \x01(\t\x12\x0e\n\x06method\x18\t \x01(\t\x12\x14\n\x0cmonth_method\x18\n \x01(\t\x12\x18\n\x10sub_month_method\x18\x0b \x01(\t\x12\x13\n\x0bweek_method\x18\x0c \x01(\t\x12\x17\n\x0fsub_week_method\x18\r \x01(\t\x12\x12\n\nday_method\x18\x0e \x01(\t\x12\x16\n\x0esub_day_method\x18\x0f \x01(\t\x12\x10\n\x08interval\x18\x10 \x01(\r\x12\x15\n\rbranch_method\x18\x11 \x01(\t\x12\x16\n\x0eproduct_method\x18\x12 \x01(\t\x12\x11\n\tstore_ids\x18\x13 \x03(\x04\x12\x12\n\nbranch_ids\x18\x14 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x15 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x16 \x03(\x04\x12\x0e\n\x06remark\x18\x17 \x01(\t\x12)\n\x05start\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x64oc_deadline\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ndoc_cancel\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0bis_recreate\x18\x1c \x01(\x08\x12\x10\n\x08sub_type\x18\x1d \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x1e \x01(\t\x12\x12\n\nstore_type\x18\x1f \x01(\t\x12\x0e\n\x06\x65xtend\x18  \x01(\t\x12\x0e\n\x06\x64omain\x18! \x01(\t\x12\x0e\n\x06module\x18# \x01(\t\x12\n\n\x02tz\x18$ \x01(\t\x12\x1a\n\x12\x63\x61lc_by_safety_qty\x18% \x01(\x08\"5\n\x12\x43reatePlanResponse\x12\x0f\n\x07plan_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\x08\"\xce\x01\n\x12GetPlanByIDRequest\x12\x0f\n\x07plan_id\x18\x01 \x01(\x04\x12\x17\n\x0finclude_product\x18\x02 \x01(\x08\x12\x18\n\x10include_category\x18\x03 \x01(\x08\x12\x16\n\x0einclude_branch\x18\x04 \x01(\x08\x12\x15\n\rinclude_store\x18\x05 \x01(\x08\x12\x12\n\nstore_type\x18\x06 \x01(\t\x12\x11\n\tplan_type\x18\x07 \x01(\t\x12\x0e\n\x06\x64omain\x18\n \x01(\t\x12\x0e\n\x06module\x18\x0f \x01(\t\"\xe5\x02\n\nPlanBranch\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x0f\n\x07plan_id\x18\x02 \x01(\x04\x12\x0f\n\x07\x64\x65leted\x18\x03 \x01(\x08\x12\x13\n\x0b\x62ranch_code\x18\x04 \x01(\t\x12\x13\n\x0b\x62ranch_name\x18\x05 \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x06 \x01(\t\x12\x12\n\ncreated_by\x18\x08 \x01(\x04\x12\x12\n\nupdated_by\x18\t \x01(\x04\x12.\n\ncreated_at\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\npartner_id\x18\x0c \x01(\x04\x12\n\n\x02id\x18\r \x01(\x04\x12\x0f\n\x07user_id\x18\x0e \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x0f \x01(\t\x12\x14\n\x0cupdated_name\x18\x10 \x01(\t\"\xe0\x02\n\tPlanStore\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x0f\n\x07plan_id\x18\x02 \x01(\x04\x12\x0f\n\x07\x64\x65leted\x18\x03 \x01(\x08\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nstore_name\x18\x05 \x01(\t\x12\x12\n\nstore_type\x18\x06 \x01(\t\x12\x12\n\ncreated_by\x18\x08 \x01(\x04\x12\x12\n\nupdated_by\x18\t \x01(\x04\x12.\n\ncreated_at\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\npartner_id\x18\x0c \x01(\x04\x12\n\n\x02id\x18\r \x01(\x04\x12\x0f\n\x07user_id\x18\x0e \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x0f \x01(\t\x12\x14\n\x0cupdated_name\x18\x10 \x01(\t\"\x80\x03\n\x0bPlanProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12\x14\n\x0cproduct_type\x18\x04 \x01(\t\x12\x12\n\ncreated_by\x18\x06 \x01(\x04\x12\x12\n\nupdated_by\x18\x07 \x01(\x04\x12\x0f\n\x07plan_id\x18\x08 \x01(\x04\x12\x0f\n\x07\x64\x65leted\x18\t \x01(\x08\x12.\n\ncreated_at\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\npartner_id\x18\x0c \x01(\x04\x12\n\n\x02id\x18\r \x01(\x04\x12\x14\n\x0cstorage_type\x18\x0e \x01(\t\x12\x0f\n\x07user_id\x18\x0f \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x10 \x01(\t\x12\x14\n\x0cupdated_name\x18\x11 \x01(\t\"\xef\x02\n\x0cPlanCategory\x12\x0f\n\x07plan_id\x18\x02 \x01(\x04\x12\x13\n\x0b\x63\x61tegory_id\x18\x03 \x01(\x04\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x15\n\rcategory_name\x18\x05 \x01(\t\x12\x15\n\rcategory_type\x18\x06 \x01(\t\x12\x12\n\ncreated_by\x18\x07 \x01(\x04\x12\x12\n\nupdated_by\x18\x08 \x01(\x04\x12\x0f\n\x07\x64\x65leted\x18\t \x01(\x08\x12.\n\ncreated_at\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\npartner_id\x18\x0c \x01(\x04\x12\n\n\x02id\x18\r \x01(\x04\x12\x0f\n\x07user_id\x18\x0e \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x0f \x01(\t\x12\x14\n\x0cupdated_name\x18\x10 \x01(\t\"\xe4\x01\n\x0eGetPlanRequest\x12\x11\n\tplan_type\x18\x01 \x01(\t\x12\x0e\n\x06status\x18\x02 \x03(\t\x12\x12\n\nbranch_ids\x18\x03 \x03(\x04\x12\x11\n\tstore_ids\x18\x04 \x03(\x04\x12\x0e\n\x06offset\x18\x05 \x01(\r\x12\r\n\x05limit\x18\x06 \x01(\r\x12\x0e\n\x06method\x18\x07 \x01(\t\x12\x13\n\x0bis_recreate\x18\x08 \x01(\x08\x12\x10\n\x08sub_type\x18\t \x01(\t\x12\x12\n\nstore_type\x18\n \x01(\t\x12\x0e\n\x06\x64omain\x18\x14 \x01(\t\x12\x0e\n\x06module\x18\x19 \x01(\t\"\xd0\x08\n\x04Plan\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x11\n\tplan_type\x18\x03 \x01(\t\x12\x0e\n\x06\x62\x65\x66ore\x18\x04 \x01(\r\x12\x1b\n\x13\x63\x61lculate_inventory\x18\x05 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x0c\n\x04name\x18\x07 \x01(\t\x12\x0e\n\x06method\x18\t \x01(\t\x12\x14\n\x0cmonth_method\x18\n \x01(\t\x12\x18\n\x10sub_month_method\x18\x0b \x01(\t\x12\x13\n\x0bweek_method\x18\x0c \x01(\t\x12\x17\n\x0fsub_week_method\x18\r \x01(\t\x12\x12\n\nday_method\x18\x0e \x01(\t\x12\x16\n\x0esub_day_method\x18\x0f \x01(\t\x12\x10\n\x08interval\x18\x10 \x01(\r\x12\x15\n\rbranch_method\x18\x11 \x01(\t\x12\x16\n\x0eproduct_method\x18\x12 \x01(\t\x12\x11\n\tstore_ids\x18\x13 \x03(\x04\x12\x12\n\nbranch_ids\x18\x14 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x15 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x16 \x03(\x04\x12\x0e\n\x06remark\x18\x17 \x01(\t\x12)\n\x05start\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\n\n\x02id\x18\x1a \x01(\x04\x12.\n\ncreated_at\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x1d \x01(\x04\x12\x12\n\nupdated_by\x18\x1e \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1f \x01(\t\x12\x14\n\x0cupdated_name\x18  \x01(\t\x12\x0e\n\x06status\x18! \x01(\t\x12\x30\n\x0cstart_before\x18\" \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x64oc_deadline\x18# \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ndoc_cancel\x18$ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12-\n\tlast_time\x18% \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12-\n\tnext_time\x18& \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0bis_recreate\x18\' \x01(\x08\x12\x10\n\x08sub_type\x18( \x01(\t\x12\x12\n\nstore_type\x18) \x01(\t\x12\x0e\n\x06\x65xtend\x18* \x01(\t\x12\n\n\x02tz\x18+ \x01(\t\x12\x1a\n\x12\x63\x61lc_by_safety_qty\x18, \x01(\x08\"\xc7\t\n\x08PlanInfo\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x11\n\tplan_type\x18\x03 \x01(\t\x12\x0e\n\x06\x62\x65\x66ore\x18\x04 \x01(\r\x12\x1b\n\x13\x63\x61lculate_inventory\x18\x05 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x0c\n\x04name\x18\x07 \x01(\t\x12\x0e\n\x06method\x18\t \x01(\t\x12\x14\n\x0cmonth_method\x18\n \x01(\t\x12\x18\n\x10sub_month_method\x18\x0b \x01(\t\x12\x13\n\x0bweek_method\x18\x0c \x01(\t\x12\x17\n\x0fsub_week_method\x18\r \x01(\t\x12\x12\n\nday_method\x18\x0e \x01(\t\x12\x16\n\x0esub_day_method\x18\x0f \x01(\t\x12\x10\n\x08interval\x18\x10 \x01(\r\x12\x15\n\rbranch_method\x18\x11 \x01(\t\x12\x16\n\x0eproduct_method\x18\x12 \x01(\t\x12\'\n\nplan_store\x18\x13 \x03(\x0b\x32\x13.doc_plan.PlanStore\x12)\n\x0bplan_branch\x18\x14 \x03(\x0b\x32\x14.doc_plan.PlanBranch\x12+\n\x0cplan_product\x18\x15 \x03(\x0b\x32\x15.doc_plan.PlanProduct\x12-\n\rplan_category\x18\x16 \x03(\x0b\x32\x16.doc_plan.PlanCategory\x12\x0e\n\x06remark\x18\x17 \x01(\t\x12)\n\x05start\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\n\n\x02id\x18\x1a \x01(\x04\x12.\n\ncreated_at\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x1d \x01(\x04\x12\x12\n\nupdated_by\x18\x1e \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1f \x01(\t\x12\x14\n\x0cupdated_name\x18  \x01(\t\x12\x0e\n\x06status\x18! \x01(\t\x12\x30\n\x0cstart_before\x18\" \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x64oc_deadline\x18# \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ndoc_cancel\x18$ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12-\n\tlast_time\x18% \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12-\n\tnext_time\x18& \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0bis_recreate\x18\' \x01(\x08\x12\x10\n\x08sub_type\x18( \x01(\t\x12\x12\n\nstore_type\x18) \x01(\t\x12\x0e\n\x06\x65xtend\x18* \x01(\t\x12\x13\n\x0b\x62ranch_type\x18, \x01(\t\x12\n\n\x02tz\x18- \x01(\t\x12\x1a\n\x12\x63\x61lc_by_safety_qty\x18. \x01(\x08\">\n\x0fGetPlanResponse\x12\x1c\n\x04rows\x18\x01 \x03(\x0b\x32\x0e.doc_plan.Plan\x12\r\n\x05total\x18\x02 \x01(\r\"\xc1\x06\n\x0ePutPlanRequest\x12\x0f\n\x07plan_id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x11\n\tplan_type\x18\x03 \x01(\t\x12\x0e\n\x06\x62\x65\x66ore\x18\x04 \x01(\r\x12\x1b\n\x13\x63\x61lculate_inventory\x18\x05 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x0c\n\x04name\x18\x07 \x01(\t\x12\x0e\n\x06method\x18\t \x01(\t\x12\x14\n\x0cmonth_method\x18\n \x01(\t\x12\x18\n\x10sub_month_method\x18\x0b \x01(\t\x12\x13\n\x0bweek_method\x18\x0c \x01(\t\x12\x17\n\x0fsub_week_method\x18\r \x01(\t\x12\x12\n\nday_method\x18\x0e \x01(\t\x12\x16\n\x0esub_day_method\x18\x0f \x01(\t\x12\x10\n\x08interval\x18\x10 \x01(\r\x12\x15\n\rbranch_method\x18\x11 \x01(\t\x12\x16\n\x0eproduct_method\x18\x12 \x01(\t\x12\x11\n\tstore_ids\x18\x13 \x03(\x04\x12\x12\n\nbranch_ids\x18\x14 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x15 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x16 \x03(\x04\x12\x0e\n\x06remark\x18\x17 \x01(\t\x12)\n\x05start\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0bonly_remark\x18\x1a \x01(\x08\x12\x30\n\x0c\x64oc_deadline\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ndoc_cancel\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0bis_recreate\x18\x1d \x01(\x08\x12\x10\n\x08sub_type\x18\x1e \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x1f \x01(\t\x12\x12\n\nstore_type\x18  \x01(\t\x12\x0e\n\x06\x65xtend\x18! \x01(\t\x12\x0e\n\x06\x64omain\x18# \x01(\t\x12\x0e\n\x06module\x18$ \x01(\t\x12\n\n\x02tz\x18% \x01(\t\x12\x1a\n\x12\x63\x61lc_by_safety_qty\x18& \x01(\x08\"!\n\x0fPutPlanResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xbc\x01\n\x11\x41\x63tionPlanRequest\x12\x0f\n\x07plan_id\x18\x01 \x01(\x04\x12\x32\n\x06\x61\x63tion\x18\x02 \x01(\x0e\x32\".doc_plan.ActionPlanRequest.Action\x12\x11\n\tplan_type\x18\x03 \x01(\t\x12\x0e\n\x06\x64omain\x18\x05 \x01(\t\x12\x0e\n\x06module\x18\x06 \x01(\t\"/\n\x06\x41\x63tion\x12\x0b\n\x07\x63onfirm\x10\x00\x12\n\n\x06\x63\x61ncel\x10\x01\x12\x0c\n\x08\x63\x61llback\x10\x02\"$\n\x12\x41\x63tionPlanResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\x89\x01\n\x10\x43reateDocRequest\x12\x30\n\x0crequest_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tplan_type\x18\x02 \x03(\t\x12\x10\n\x08sub_type\x18\x03 \x03(\t\x12\x0e\n\x06\x64omain\x18\x04 \x01(\t\x12\x0e\n\x06module\x18\x05 \x01(\t\"U\n\x11\x43reateDocResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x30\n\x0crequest_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xca\x01\n\x1fModifyPlanProductByStoreRequest\x12\x0f\n\x07plan_id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x04 \x03(\x04\x12\x16\n\x0eproduct_method\x18\n \x01(\t\x12\x0e\n\x06reason\x18\x05 \x01(\t\x12\x11\n\tplan_type\x18\x06 \x01(\t\x12\x0e\n\x06\x64omain\x18\x08 \x01(\t\x12\x0e\n\x06module\x18\t \x01(\t\"2\n ModifyPlanProductByStoreResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"t\n\x1cGetPlanProductByStoreRequest\x12\x0f\n\x07plan_id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x11\n\tplan_type\x18\x03 \x01(\t\x12\x0e\n\x06\x64omain\x18\x05 \x01(\t\x12\x0e\n\x06module\x18\x06 \x01(\t\"\xbc\x03\n\x12PlanProductByStore\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12\x14\n\x0cproduct_type\x18\x04 \x01(\t\x12\x12\n\ncreated_by\x18\x06 \x01(\x04\x12\x12\n\nupdated_by\x18\x07 \x01(\x04\x12\x0f\n\x07plan_id\x18\x08 \x01(\x04\x12\x0f\n\x07\x64\x65leted\x18\t \x01(\x08\x12.\n\ncreated_at\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\npartner_id\x18\x0c \x01(\x04\x12\n\n\x02id\x18\r \x01(\x04\x12\x14\n\x0cstorage_type\x18\x0e \x01(\t\x12\x0f\n\x07user_id\x18\x0f \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x10 \x01(\t\x12\x14\n\x0cupdated_name\x18\x11 \x01(\t\x12\x11\n\tmodify_id\x18\x12 \x01(\x04\x12\x0e\n\x06log_id\x18\x13 \x01(\x04\x12\x10\n\x08store_id\x18\x14 \x01(\x04\"\xab\x03\n\x13PlanCategoryByStore\x12\x0f\n\x07plan_id\x18\x02 \x01(\x04\x12\x13\n\x0b\x63\x61tegory_id\x18\x03 \x01(\x04\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x15\n\rcategory_name\x18\x05 \x01(\t\x12\x15\n\rcategory_type\x18\x06 \x01(\t\x12\x12\n\ncreated_by\x18\x07 \x01(\x04\x12\x12\n\nupdated_by\x18\x08 \x01(\x04\x12\x0f\n\x07\x64\x65leted\x18\t \x01(\x08\x12.\n\ncreated_at\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\npartner_id\x18\x0c \x01(\x04\x12\n\n\x02id\x18\r \x01(\x04\x12\x0f\n\x07user_id\x18\x0e \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x0f \x01(\t\x12\x14\n\x0cupdated_name\x18\x10 \x01(\t\x12\x11\n\tmodify_id\x18\x11 \x01(\x04\x12\x0e\n\x06log_id\x18\x12 \x01(\x04\x12\x10\n\x08store_id\x18\x13 \x01(\x04\"\xd5\x01\n\x1dGetPlanProductByStoreResponse\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12;\n\x15plan_product_by_store\x18\x02 \x03(\x0b\x32\x1c.doc_plan.PlanProductByStore\x12=\n\x16plan_category_by_store\x18\x03 \x03(\x0b\x32\x1d.doc_plan.PlanCategoryByStore\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x16\n\x0eproduct_method\x18\x05 \x01(\t\"\xf4\x01\n\x1f\x41\x63tionPlanProductByStoreRequest\x12\x0f\n\x07plan_id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12@\n\x06\x61\x63tion\x18\x03 \x01(\x0e\x32\x30.doc_plan.ActionPlanProductByStoreRequest.Action\x12\x11\n\tplan_type\x18\x04 \x01(\t\x12\x0e\n\x06\x64omain\x18\x05 \x01(\t\x12\x0e\n\x06module\x18\x06 \x01(\t\"9\n\x06\x41\x63tion\x12\x08\n\x04null\x10\x00\x12\n\n\x06submit\x10\x01\x12\x0b\n\x07\x61pprove\x10\x02\x12\x0c\n\x08\x63\x61llback\x10\x03\"2\n ActionPlanProductByStoreResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"|\n\x17GetStorePlanListRequest\x12\x0f\n\x07plan_id\x18\x01 \x01(\x04\x12\x0e\n\x06offset\x18\x02 \x01(\x03\x12\r\n\x05limit\x18\x03 \x01(\x03\x12\x11\n\tplan_type\x18\x04 \x01(\t\x12\x0e\n\x06\x64omain\x18\x05 \x01(\t\x12\x0e\n\x06module\x18\x06 \x01(\t\"\x85\x03\n\rStorePlanList\x12\x0f\n\x07plan_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x10\n\x08store_id\x18\x03 \x01(\x04\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nstore_name\x18\x05 \x01(\t\x12\x1a\n\x12stocktake_quantity\x18\x06 \x01(\x01\x12\x14\n\x0c\x61\x64\x64_quantity\x18\x07 \x01(\x01\x12\x17\n\x0freduce_quantity\x18\x08 \x01(\x01\x12\x14\n\x0cupdated_name\x18\t \x01(\t\x12.\n\nupdated_at\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x10product_by_store\x18\x0b \x03(\x0b\x32\x1c.doc_plan.PlanProductByStore\x12\x38\n\x11\x63\x61tegory_by_store\x18\x0c \x03(\x0b\x32\x1d.doc_plan.PlanCategoryByStore\x12\x16\n\x0eproduct_method\x18\r \x01(\t\"\x92\x01\n\x18GetStorePlanListResponse\x12%\n\x04rows\x18\x01 \x03(\x0b\x32\x17.doc_plan.StorePlanList\x12\r\n\x05total\x18\x02 \x01(\x04\x12@\n\x15machining_center_rows\x18\x03 \x03(\x0b\x32!.doc_plan.MachiningCenterPlanList\"\x94\x01\n\x14GetPlanStreamRequest\x12\x11\n\tplan_type\x18\x01 \x01(\t\x12\x0f\n\x07plan_id\x18\x02 \x01(\x04\x12\x10\n\x08store_id\x18\x03 \x01(\x04\x12\x11\n\tget_store\x18\x04 \x01(\x08\x12\x13\n\x0bget_product\x18\x05 \x01(\x08\x12\x0e\n\x06\x64omain\x18\x06 \x01(\t\x12\x0e\n\x06module\x18\x08 \x01(\t\"\xe7\x01\n\x0fPlanStreamStore\x12/\n\x0btarget_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rplan_quantity\x18\x02 \x01(\x01\x12\x15\n\rreal_quantity\x18\x03 \x01(\x01\x12\x14\n\x0cupdated_name\x18\x04 \x01(\t\x12.\n\nupdated_at\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0bstore_infos\x18\x06 \x03(\x0b\x32\x1a.doc_plan.UncompletedStore\"8\n\x10UncompletedStore\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x12\n\nstore_name\x18\x02 \x01(\t\"\xc8\x01\n\x11PlanStreamProduct\x12/\n\x0btarget_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rplan_quantity\x18\x02 \x01(\x01\x12\x15\n\rreal_quantity\x18\x03 \x01(\x01\x12\x14\n\x0cupdated_name\x18\x04 \x01(\t\x12.\n\nupdated_at\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06\x64oc_id\x18\x06 \x01(\x04\"\x9f\x01\n\x15GetPlanStreamResponse\x12\x30\n\rstream_stores\x18\x01 \x03(\x0b\x32\x19.doc_plan.PlanStreamStore\x12\x45\n\x18stream_position_products\x18\x02 \x03(\x0b\x32#.doc_plan.PlanStreamPositionProduct\x12\r\n\x05total\x18\x03 \x01(\x04\"\xee\x01\n\x18GetPlanByStoreIDSRequest\x12\x11\n\tplan_type\x18\x01 \x01(\t\x12\x0e\n\x06status\x18\x02 \x03(\t\x12\x12\n\nbranch_ids\x18\x03 \x03(\x04\x12\x11\n\tstore_ids\x18\x04 \x03(\x04\x12\x0e\n\x06offset\x18\x05 \x01(\r\x12\r\n\x05limit\x18\x06 \x01(\r\x12\x0e\n\x06method\x18\x07 \x01(\t\x12\x13\n\x0bis_recreate\x18\x08 \x01(\x08\x12\x10\n\x08sub_type\x18\t \x01(\t\x12\x12\n\nstore_type\x18\n \x01(\t\x12\x0e\n\x06\x64omain\x18\x0b \x01(\t\x12\x0e\n\x06module\x18\x0c \x01(\t\"\'\n\tStoreInfo\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\"(\n\nBranchInfo\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\"\xb4\x08\n\x0ePlanByStoreIDS\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x11\n\tplan_type\x18\x03 \x01(\t\x12\x0e\n\x06\x62\x65\x66ore\x18\x04 \x01(\r\x12\x1b\n\x13\x63\x61lculate_inventory\x18\x05 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x0c\n\x04name\x18\x07 \x01(\t\x12\x0e\n\x06method\x18\t \x01(\t\x12\x14\n\x0cmonth_method\x18\n \x01(\t\x12\x18\n\x10sub_month_method\x18\x0b \x01(\t\x12\x13\n\x0bweek_method\x18\x0c \x01(\t\x12\x17\n\x0fsub_week_method\x18\r \x01(\t\x12\x12\n\nday_method\x18\x0e \x01(\t\x12\x16\n\x0esub_day_method\x18\x0f \x01(\t\x12\x10\n\x08interval\x18\x10 \x01(\r\x12\x15\n\rbranch_method\x18\x11 \x01(\t\x12\x16\n\x0eproduct_method\x18\x12 \x01(\t\x12\'\n\nstore_info\x18\x13 \x03(\x0b\x32\x13.doc_plan.StoreInfo\x12)\n\x0b\x62ranch_info\x18\x14 \x03(\x0b\x32\x14.doc_plan.BranchInfo\x12\x0e\n\x06remark\x18\x17 \x01(\t\x12)\n\x05start\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\n\n\x02id\x18\x1a \x01(\x04\x12.\n\ncreated_at\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x1d \x01(\x04\x12\x12\n\nupdated_by\x18\x1e \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1f \x01(\t\x12\x14\n\x0cupdated_name\x18  \x01(\t\x12\x0e\n\x06status\x18! \x01(\t\x12\x30\n\x0cstart_before\x18\" \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x64oc_deadline\x18# \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ndoc_cancel\x18$ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12-\n\tlast_time\x18% \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12-\n\tnext_time\x18& \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0bis_recreate\x18\' \x01(\x08\x12\x10\n\x08sub_type\x18( \x01(\t\x12\x0e\n\x06\x65xtend\x18) \x01(\t\x12\x12\n\nstore_type\x18* \x01(\t\"R\n\x19GetPlanByStoreIDSResponse\x12&\n\x04rows\x18\x01 \x03(\x0b\x32\x18.doc_plan.PlanByStoreIDS\x12\r\n\x05total\x18\x02 \x01(\r\"r\n\x1aPreviewDocByStoreIDRequest\x12\x0f\n\x07plan_id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x11\n\tplan_type\x18\x03 \x01(\t\x12\x0e\n\x06\x64omain\x18\x05 \x01(\t\x12\x0e\n\x06module\x18\x06 \x01(\t\"\xe1\x01\n\x11PreviewDocProduct\x12\x14\n\x0cproduct_code\x18\x01 \x01(\t\x12\x14\n\x0cproduct_name\x18\x02 \x01(\t\x12\x37\n\x05units\x18\x03 \x03(\x0b\x32(.doc_plan.PreviewDocProduct.ProductUnits\x12\r\n\x05model\x18\x04 \x01(\t\x1aX\n\x0cProductUnits\x12\x0f\n\x07unit_id\x18\x01 \x01(\x04\x12\x11\n\tunit_name\x18\x02 \x01(\t\x12\x11\n\tunit_spec\x18\x03 \x01(\t\x12\x11\n\tunit_rate\x18\x04 \x01(\x01\"W\n\x1bPreviewDocByStoreIDResponse\x12)\n\x04rows\x18\x01 \x03(\x0b\x32\x1b.doc_plan.PreviewDocProduct\x12\r\n\x05total\x18\x02 \x01(\r\"t\n\x17GetPlanDocStatusRequest\x12\x0f\n\x07plan_id\x18\x01 \x01(\x04\x12\x11\n\tplan_type\x18\x02 \x01(\t\x12\x0e\n\x06\x64omain\x18\x03 \x01(\t\x12\x0e\n\x06module\x18\x05 \x01(\t\x12\x15\n\rorder_type_id\x18\x06 \x01(\x04\"\xb3\x01\n\rPlanDocStatus\x12\x0c\n\x04time\x18\x01 \x01(\t\x12\x13\n\x0btime_around\x18\x02 \x01(\t\x12\x14\n\x0cstart_status\x18\x03 \x03(\t\x12\x12\n\nend_status\x18\x04 \x01(\t\x12\n\n\x02id\x18\x05 \x01(\x04\x12\x0f\n\x07plan_id\x18\x06 \x01(\x04\x12\x12\n\ndoc_filter\x18\x07 \x01(\t\x12\x15\n\rorder_type_id\x18\x08 \x01(\x04\x12\r\n\x05state\x18\t \x01(\t\"R\n\x18GetPlanDocStatusResponse\x12%\n\x04rows\x18\x01 \x03(\x0b\x32\x17.doc_plan.PlanDocStatus\x12\x0f\n\x07plan_id\x18\x02 \x01(\x04\"\xa4\x02\n\x1a\x41\x63tionPlanDocStatusRequest\x12\x0f\n\x07plan_id\x18\x01 \x01(\x04\x12;\n\x06\x61\x63tion\x18\x02 \x01(\x0e\x32+.doc_plan.ActionPlanDocStatusRequest.Action\x12%\n\x04rows\x18\x03 \x03(\x0b\x32\x17.doc_plan.PlanDocStatus\x12\x12\n\nrequest_id\x18\x04 \x01(\x04\x12\x11\n\tplan_type\x18\x05 \x01(\t\x12\x0e\n\x06\x64omain\x18\x06 \x01(\t\x12\x0e\n\x06module\x18\x07 \x01(\t\x12\x15\n\rorder_type_id\x18\x08 \x01(\x04\"3\n\x06\x41\x63tion\x12\x08\n\x04null\x10\x00\x12\x07\n\x03\x61\x64\x64\x10\x01\x12\n\n\x06update\x10\x02\x12\n\n\x06\x64\x65lete\x10\x03\"-\n\x1b\x41\x63tionPlanDocStatusResponse\x12\x0e\n\x06result\x18\x02 \x01(\t\"\x89\x01\n\x1dGetPlanPositionProductRequest\x12\x0f\n\x07plan_id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_type\x18\x03 \x01(\t\x12\x11\n\tplan_type\x18\x04 \x01(\t\x12\x0e\n\x06\x64omain\x18\x05 \x01(\t\x12\x0e\n\x06module\x18\x06 \x01(\t\"Q\n\x0fPositionProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\"Y\n\x14PlanPositionCategory\x12\x13\n\x0b\x63\x61tegory_id\x18\x01 \x01(\x04\x12\x15\n\rcategory_code\x18\x02 \x01(\t\x12\x15\n\rcategory_name\x18\x03 \x01(\t\"\xb8\x01\n\x13PlanPositionProduct\x12\x13\n\x0bposition_id\x18\x01 \x01(\x04\x12\x15\n\rposition_code\x18\x02 \x01(\t\x12\x15\n\rposition_name\x18\x03 \x01(\t\x12+\n\x08products\x18\x04 \x03(\x0b\x32\x19.doc_plan.PositionProduct\x12\x31\n\tcategorys\x18\x05 \x03(\x0b\x32\x1e.doc_plan.PlanPositionCategory\"o\n\x1eGetPlanPositionProductResponse\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12+\n\x04rows\x18\x02 \x03(\x0b\x32\x1d.doc_plan.PlanPositionProduct\x12\x0e\n\x06status\x18\x03 \x01(\t\"\xa1\x01\n\x1cUpdatePositionProductRequest\x12\x0f\n\x07plan_id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12+\n\x04rows\x18\x03 \x03(\x0b\x32\x1d.doc_plan.PlanPositionProduct\x12\x11\n\tplan_type\x18\x04 \x01(\t\x12\x0e\n\x06\x64omain\x18\x05 \x01(\t\x12\x0e\n\x06module\x18\x06 \x01(\t\"\xa3\x01\n\x19PlanStreamPositionProduct\x12\x13\n\x0bposition_id\x18\x01 \x01(\x04\x12\x15\n\rposition_code\x18\x02 \x01(\t\x12\x15\n\rposition_name\x18\x03 \x01(\t\x12\x34\n\x0fstream_products\x18\x05 \x03(\x0b\x32\x1b.doc_plan.PlanStreamProduct\x12\r\n\x05total\x18\x06 \x01(\x04\"\xf3\x03\n\x17MachiningCenterPlanList\x12\x0f\n\x07plan_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x10\n\x08store_id\x18\x03 \x01(\x04\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nstore_name\x18\x05 \x01(\t\x12\x14\n\x0cupdated_name\x18\x06 \x01(\t\x12.\n\nupdated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12I\n\rposition_rows\x18\x08 \x03(\x0b\x32\x32.doc_plan.MachiningCenterPlanList.PositionPlanList\x12\x1a\n\x12stocktake_quantity\x18\t \x01(\x01\x12\x14\n\x0c\x61\x64\x64_quantity\x18\n \x01(\x01\x12\x17\n\x0freduce_quantity\x18\x0b \x01(\x01\x1a\xa0\x01\n\x10PositionPlanList\x12\x13\n\x0bposition_id\x18\x01 \x01(\x04\x12\x15\n\rposition_code\x18\x02 \x01(\t\x12\x15\n\rposition_name\x18\x03 \x01(\t\x12\x1a\n\x12stocktake_quantity\x18\x04 \x01(\x01\x12\x14\n\x0c\x61\x64\x64_quantity\x18\x05 \x01(\x01\x12\x17\n\x0freduce_quantity\x18\x06 \x01(\x01\"\xe5\x01\n\x19GetDocPlanCalendarRequest\x12\x11\n\tplan_type\x18\x01 \x01(\t\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x0e\n\x06offset\x18\x05 \x01(\r\x12\r\n\x05limit\x18\x06 \x01(\r\x12\x12\n\nstore_type\x18\x07 \x01(\t\x12.\n\nstart_date\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nutc_offset\x18\n \x01(\r\"Q\n\x1aGetDocPlanCalendarResponse\x12$\n\x04rows\x18\x01 \x03(\x0b\x32\x16.doc_plan.PlanCalendar\x12\r\n\x05total\x18\x02 \x01(\r\"\xf3\x02\n\x0cPlanCalendar\x12\x0f\n\x07plan_id\x18\x01 \x01(\x04\x12\x0e\n\x06\x62\x65\x66ore\x18\x02 \x01(\r\x12\x11\n\tplan_type\x18\x03 \x01(\t\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\x0c\n\x04name\x18\x05 \x01(\t\x12\x0e\n\x06method\x18\x06 \x01(\t\x12\x16\n\x0eplan_date_list\x18\n \x03(\t\x12-\n\tplan_time\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12)\n\x05start\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x0e \x01(\t\x12\x12\n\nstore_type\x18\x0f \x01(\t\x12\x13\n\x0btime_around\x18\x10 \x01(\t\x12/\n\x0e\x63ontrol_status\x18\x11 \x03(\x0b\x32\x17.doc_plan.PlanDocStatus\"\x99\x01\n\x19GetPlanProductByIDRequest\x12\x0f\n\x07plan_id\x18\x01 \x01(\x04\x12\x15\n\rreturn_fields\x18\x02 \x01(\t\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06offset\x18\x04 \x01(\x05\x12\x15\n\rinclude_total\x18\x05 \x01(\x08\x12\x0e\n\x06\x64omain\x18\n \x01(\t\x12\x0e\n\x06module\x18\x0f \x01(\t\"K\n\x1aGetPlanProductByIDResponse\x12\x1e\n\x04rows\x18\x01 \x03(\x0b\x32\x10.product.Product\x12\r\n\x05total\x18\x02 \x01(\r2\xe9\x17\n\x07\x44ocPlan\x12v\n\nCreatePlan\x12\x1b.doc_plan.CreatePlanRequest\x1a\x1c.doc_plan.CreatePlanResponse\"-\x82\xd3\xe4\x93\x02\'\"\"/api/v2/doc_plan/{domain}/{module}:\x01*\x12u\n\x0bGetPlanByID\x12\x1c.doc_plan.GetPlanByIDRequest\x1a\x12.doc_plan.PlanInfo\"4\x82\xd3\xe4\x93\x02.\x12,/api/v2/doc_plan/{domain}/{module}/{plan_id}\x12j\n\x07GetPlan\x12\x18.doc_plan.GetPlanRequest\x1a\x19.doc_plan.GetPlanResponse\"*\x82\xd3\xe4\x93\x02$\x12\"/api/v2/doc_plan/{domain}/{module}\x12w\n\x07PutPlan\x12\x18.doc_plan.PutPlanRequest\x1a\x19.doc_plan.PutPlanResponse\"7\x82\xd3\xe4\x93\x02\x31\x1a,/api/v2/doc_plan/{domain}/{module}/{plan_id}:\x01*\x12\x89\x01\n\nActionPlan\x12\x1b.doc_plan.ActionPlanRequest\x1a\x1c.doc_plan.ActionPlanResponse\"@\x82\xd3\xe4\x93\x02:\x1a\x35/api/v2/doc_plan/{domain}/{module}/{plan_id}/{action}:\x01*\x12~\n\tCreateDoc\x12\x1a.doc_plan.CreateDocRequest\x1a\x1b.doc_plan.CreateDocResponse\"8\x82\xd3\xe4\x93\x02\x32\"-/api/v2/doc_plan/{domain}/{module}/batch/init:\x01*\x12\xaf\x01\n\x18ModifyPlanProductByStore\x12).doc_plan.ModifyPlanProductByStoreRequest\x1a*.doc_plan.ModifyPlanProductByStoreResponse\"<\x82\xd3\xe4\x93\x02\x36\"1/api/v2/doc_plan/{domain}/{module}/modify/product:\x01*\x12\xa9\x01\n\x15GetPlanProductByStore\x12&.doc_plan.GetPlanProductByStoreRequest\x1a\'.doc_plan.GetPlanProductByStoreResponse\"?\x82\xd3\xe4\x93\x02\x39\x12\x37/api/v2/doc_plan/{domain}/{module}/modify/product/query\x12\xb6\x01\n\x18\x41\x63tionPlanProductByStore\x12).doc_plan.ActionPlanProductByStoreRequest\x1a*.doc_plan.ActionPlanProductByStoreResponse\"C\x82\xd3\xe4\x93\x02=\x1a\x38/api/v2/doc_plan/{domain}/{module}/modify/product/status:\x01*\x12\x92\x01\n\x10GetStorePlanList\x12!.doc_plan.GetStorePlanListRequest\x1a\".doc_plan.GetStorePlanListResponse\"7\x82\xd3\xe4\x93\x02\x31\x12//api/v2/doc_plan/{domain}/{module}/modify/store\x12\x87\x01\n\rGetPlanStream\x12\x1e.doc_plan.GetPlanStreamRequest\x1a\x1f.doc_plan.GetPlanStreamResponse\"5\x82\xd3\xe4\x93\x02/\x12-/api/v2/doc_plan/{domain}/{module}/stream/log\x12\x98\x01\n\x11GetPlanByStoreIDS\x12\".doc_plan.GetPlanByStoreIDSRequest\x1a#.doc_plan.GetPlanByStoreIDSResponse\":\x82\xd3\xe4\x93\x02\x34\"//api/v2/doc_plan/{domain}/{module}/by_store_ids:\x01*\x12\x9a\x01\n\x13PreviewDocByStoreID\x12$.doc_plan.PreviewDocByStoreIDRequest\x1a%.doc_plan.PreviewDocByStoreIDResponse\"6\x82\xd3\xe4\x93\x02\x30\x12./api/v2/doc_plan/{domain}/{module}/preview/doc\x12\x95\x01\n\x10GetPlanDocStatus\x12!.doc_plan.GetPlanDocStatusRequest\x1a\".doc_plan.GetPlanDocStatusResponse\":\x82\xd3\xe4\x93\x02\x34\x12\x32/api/v2/doc_plan/{domain}/{module}/plan_doc/status\x12\xa1\x01\n\x13\x41\x63tionPlanDocStatus\x12$.doc_plan.ActionPlanDocStatusRequest\x1a%.doc_plan.ActionPlanDocStatusResponse\"=\x82\xd3\xe4\x93\x02\x37\"2/api/v2/doc_plan/{domain}/{module}/plan_doc/status:\x01*\x12\xab\x01\n\x16GetPlanPositionProduct\x12\'.doc_plan.GetPlanPositionProductRequest\x1a(.doc_plan.GetPlanPositionProductResponse\">\x82\xd3\xe4\x93\x02\x38\"3/api/v2/doc_plan/{domain}/{module}/product/position:\x01*\x12\xb2\x01\n\x19UpdatePlanPositionProduct\x12&.doc_plan.UpdatePositionProductRequest\x1a%.doc_plan.ActionPlanDocStatusResponse\"F\x82\xd3\xe4\x93\x02@\x1a;/api/v2/doc_plan/{domain}/{module}/product/position/replace:\x01*\x12\xb4\x01\n\x19\x41\x63tionPlanProductPosition\x12).doc_plan.ActionPlanProductByStoreRequest\x1a%.doc_plan.ActionPlanDocStatusResponse\"E\x82\xd3\xe4\x93\x02?\x1a:/api/v2/doc_plan/{domain}/{module}/product/position/status:\x01*\x12\x99\x01\n\x12GetDocPlanCalendar\x12#.doc_plan.GetDocPlanCalendarRequest\x1a$.doc_plan.GetDocPlanCalendarResponse\"8\x82\xd3\xe4\x93\x02\x32\x12\x30/api/v2/doc_plan/mobile/operation/calendar/query\x12\x9d\x01\n\x12GetPlanProductByID\x12#.doc_plan.GetPlanProductByIDRequest\x1a$.doc_plan.GetPlanProductByIDResponse\"<\x82\xd3\xe4\x93\x02\x36\x12\x34/api/v2/doc_plan/{domain}/{module}/{plan_id}/productb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_empty__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,metadata_dot_product__pb2.DESCRIPTOR,])



_ACTIONPLANREQUEST_ACTION = _descriptor.EnumDescriptor(
  name='Action',
  full_name='doc_plan.ActionPlanRequest.Action',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='confirm', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='cancel', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='callback', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=6371,
  serialized_end=6418,
)
_sym_db.RegisterEnumDescriptor(_ACTIONPLANREQUEST_ACTION)

_ACTIONPLANPRODUCTBYSTOREREQUEST_ACTION = _descriptor.EnumDescriptor(
  name='Action',
  full_name='doc_plan.ActionPlanProductByStoreRequest.Action',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='null', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='submit', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='approve', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='callback', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=8341,
  serialized_end=8398,
)
_sym_db.RegisterEnumDescriptor(_ACTIONPLANPRODUCTBYSTOREREQUEST_ACTION)

_ACTIONPLANDOCSTATUSREQUEST_ACTION = _descriptor.EnumDescriptor(
  name='Action',
  full_name='doc_plan.ActionPlanDocStatusRequest.Action',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='null', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='add', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='update', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='delete', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=12473,
  serialized_end=12524,
)
_sym_db.RegisterEnumDescriptor(_ACTIONPLANDOCSTATUSREQUEST_ACTION)


_NULL = _descriptor.Descriptor(
  name='Null',
  full_name='doc_plan.Null',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=144,
  serialized_end=150,
)


_PONG = _descriptor.Descriptor(
  name='Pong',
  full_name='doc_plan.Pong',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='msg', full_name='doc_plan.Pong.msg', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=152,
  serialized_end=171,
)


_CREATEPLANREQUEST = _descriptor.Descriptor(
  name='CreatePlanRequest',
  full_name='doc_plan.CreatePlanRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='doc_plan.CreatePlanRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='doc_plan.CreatePlanRequest.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.CreatePlanRequest.plan_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='before', full_name='doc_plan.CreatePlanRequest.before', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_inventory', full_name='doc_plan.CreatePlanRequest.calculate_inventory', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='doc_plan.CreatePlanRequest.code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='doc_plan.CreatePlanRequest.name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='method', full_name='doc_plan.CreatePlanRequest.method', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='month_method', full_name='doc_plan.CreatePlanRequest.month_method', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_month_method', full_name='doc_plan.CreatePlanRequest.sub_month_method', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='week_method', full_name='doc_plan.CreatePlanRequest.week_method', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_week_method', full_name='doc_plan.CreatePlanRequest.sub_week_method', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='day_method', full_name='doc_plan.CreatePlanRequest.day_method', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_day_method', full_name='doc_plan.CreatePlanRequest.sub_day_method', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='interval', full_name='doc_plan.CreatePlanRequest.interval', index=14,
      number=16, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_method', full_name='doc_plan.CreatePlanRequest.branch_method', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_method', full_name='doc_plan.CreatePlanRequest.product_method', index=16,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='doc_plan.CreatePlanRequest.store_ids', index=17,
      number=19, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='doc_plan.CreatePlanRequest.branch_ids', index=18,
      number=20, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='doc_plan.CreatePlanRequest.product_ids', index=19,
      number=21, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='doc_plan.CreatePlanRequest.category_ids', index=20,
      number=22, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='doc_plan.CreatePlanRequest.remark', index=21,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='doc_plan.CreatePlanRequest.start', index=22,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='doc_plan.CreatePlanRequest.end', index=23,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_deadline', full_name='doc_plan.CreatePlanRequest.doc_deadline', index=24,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_cancel', full_name='doc_plan.CreatePlanRequest.doc_cancel', index=25,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_recreate', full_name='doc_plan.CreatePlanRequest.is_recreate', index=26,
      number=28, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='doc_plan.CreatePlanRequest.sub_type', index=27,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='doc_plan.CreatePlanRequest.branch_type', index=28,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='doc_plan.CreatePlanRequest.store_type', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extend', full_name='doc_plan.CreatePlanRequest.extend', index=30,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='doc_plan.CreatePlanRequest.domain', index=31,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module', full_name='doc_plan.CreatePlanRequest.module', index=32,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tz', full_name='doc_plan.CreatePlanRequest.tz', index=33,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calc_by_safety_qty', full_name='doc_plan.CreatePlanRequest.calc_by_safety_qty', index=34,
      number=37, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=174,
  serialized_end=992,
)


_CREATEPLANRESPONSE = _descriptor.Descriptor(
  name='CreatePlanResponse',
  full_name='doc_plan.CreatePlanResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.CreatePlanResponse.plan_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='doc_plan.CreatePlanResponse.result', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=994,
  serialized_end=1047,
)


_GETPLANBYIDREQUEST = _descriptor.Descriptor(
  name='GetPlanByIDRequest',
  full_name='doc_plan.GetPlanByIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.GetPlanByIDRequest.plan_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_product', full_name='doc_plan.GetPlanByIDRequest.include_product', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_category', full_name='doc_plan.GetPlanByIDRequest.include_category', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_branch', full_name='doc_plan.GetPlanByIDRequest.include_branch', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_store', full_name='doc_plan.GetPlanByIDRequest.include_store', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='doc_plan.GetPlanByIDRequest.store_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.GetPlanByIDRequest.plan_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='doc_plan.GetPlanByIDRequest.domain', index=7,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module', full_name='doc_plan.GetPlanByIDRequest.module', index=8,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1050,
  serialized_end=1256,
)


_PLANBRANCH = _descriptor.Descriptor(
  name='PlanBranch',
  full_name='doc_plan.PlanBranch',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='doc_plan.PlanBranch.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.PlanBranch.plan_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deleted', full_name='doc_plan.PlanBranch.deleted', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='doc_plan.PlanBranch.branch_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='doc_plan.PlanBranch.branch_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='doc_plan.PlanBranch.branch_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='doc_plan.PlanBranch.created_by', index=6,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='doc_plan.PlanBranch.updated_by', index=7,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='doc_plan.PlanBranch.created_at', index=8,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='doc_plan.PlanBranch.updated_at', index=9,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='doc_plan.PlanBranch.partner_id', index=10,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='doc_plan.PlanBranch.id', index=11,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='doc_plan.PlanBranch.user_id', index=12,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='doc_plan.PlanBranch.created_name', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='doc_plan.PlanBranch.updated_name', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1259,
  serialized_end=1616,
)


_PLANSTORE = _descriptor.Descriptor(
  name='PlanStore',
  full_name='doc_plan.PlanStore',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='doc_plan.PlanStore.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.PlanStore.plan_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deleted', full_name='doc_plan.PlanStore.deleted', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='doc_plan.PlanStore.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='doc_plan.PlanStore.store_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='doc_plan.PlanStore.store_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='doc_plan.PlanStore.created_by', index=6,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='doc_plan.PlanStore.updated_by', index=7,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='doc_plan.PlanStore.created_at', index=8,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='doc_plan.PlanStore.updated_at', index=9,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='doc_plan.PlanStore.partner_id', index=10,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='doc_plan.PlanStore.id', index=11,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='doc_plan.PlanStore.user_id', index=12,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='doc_plan.PlanStore.created_name', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='doc_plan.PlanStore.updated_name', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1619,
  serialized_end=1971,
)


_PLANPRODUCT = _descriptor.Descriptor(
  name='PlanProduct',
  full_name='doc_plan.PlanProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='doc_plan.PlanProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='doc_plan.PlanProduct.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='doc_plan.PlanProduct.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='doc_plan.PlanProduct.product_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='doc_plan.PlanProduct.created_by', index=4,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='doc_plan.PlanProduct.updated_by', index=5,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.PlanProduct.plan_id', index=6,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deleted', full_name='doc_plan.PlanProduct.deleted', index=7,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='doc_plan.PlanProduct.created_at', index=8,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='doc_plan.PlanProduct.updated_at', index=9,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='doc_plan.PlanProduct.partner_id', index=10,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='doc_plan.PlanProduct.id', index=11,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='doc_plan.PlanProduct.storage_type', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='doc_plan.PlanProduct.user_id', index=13,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='doc_plan.PlanProduct.created_name', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='doc_plan.PlanProduct.updated_name', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1974,
  serialized_end=2358,
)


_PLANCATEGORY = _descriptor.Descriptor(
  name='PlanCategory',
  full_name='doc_plan.PlanCategory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.PlanCategory.plan_id', index=0,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='doc_plan.PlanCategory.category_id', index=1,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='doc_plan.PlanCategory.category_code', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='doc_plan.PlanCategory.category_name', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_type', full_name='doc_plan.PlanCategory.category_type', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='doc_plan.PlanCategory.created_by', index=5,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='doc_plan.PlanCategory.updated_by', index=6,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deleted', full_name='doc_plan.PlanCategory.deleted', index=7,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='doc_plan.PlanCategory.created_at', index=8,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='doc_plan.PlanCategory.updated_at', index=9,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='doc_plan.PlanCategory.partner_id', index=10,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='doc_plan.PlanCategory.id', index=11,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='doc_plan.PlanCategory.user_id', index=12,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='doc_plan.PlanCategory.created_name', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='doc_plan.PlanCategory.updated_name', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2361,
  serialized_end=2728,
)


_GETPLANREQUEST = _descriptor.Descriptor(
  name='GetPlanRequest',
  full_name='doc_plan.GetPlanRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.GetPlanRequest.plan_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='doc_plan.GetPlanRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='doc_plan.GetPlanRequest.branch_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='doc_plan.GetPlanRequest.store_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='doc_plan.GetPlanRequest.offset', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='doc_plan.GetPlanRequest.limit', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='method', full_name='doc_plan.GetPlanRequest.method', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_recreate', full_name='doc_plan.GetPlanRequest.is_recreate', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='doc_plan.GetPlanRequest.sub_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='doc_plan.GetPlanRequest.store_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='doc_plan.GetPlanRequest.domain', index=10,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module', full_name='doc_plan.GetPlanRequest.module', index=11,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2731,
  serialized_end=2959,
)


_PLAN = _descriptor.Descriptor(
  name='Plan',
  full_name='doc_plan.Plan',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='doc_plan.Plan.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='doc_plan.Plan.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.Plan.plan_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='before', full_name='doc_plan.Plan.before', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_inventory', full_name='doc_plan.Plan.calculate_inventory', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='doc_plan.Plan.code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='doc_plan.Plan.name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='method', full_name='doc_plan.Plan.method', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='month_method', full_name='doc_plan.Plan.month_method', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_month_method', full_name='doc_plan.Plan.sub_month_method', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='week_method', full_name='doc_plan.Plan.week_method', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_week_method', full_name='doc_plan.Plan.sub_week_method', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='day_method', full_name='doc_plan.Plan.day_method', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_day_method', full_name='doc_plan.Plan.sub_day_method', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='interval', full_name='doc_plan.Plan.interval', index=14,
      number=16, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_method', full_name='doc_plan.Plan.branch_method', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_method', full_name='doc_plan.Plan.product_method', index=16,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='doc_plan.Plan.store_ids', index=17,
      number=19, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='doc_plan.Plan.branch_ids', index=18,
      number=20, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='doc_plan.Plan.product_ids', index=19,
      number=21, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='doc_plan.Plan.category_ids', index=20,
      number=22, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='doc_plan.Plan.remark', index=21,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='doc_plan.Plan.start', index=22,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='doc_plan.Plan.end', index=23,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='doc_plan.Plan.id', index=24,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='doc_plan.Plan.created_at', index=25,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='doc_plan.Plan.updated_at', index=26,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='doc_plan.Plan.created_by', index=27,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='doc_plan.Plan.updated_by', index=28,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='doc_plan.Plan.created_name', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='doc_plan.Plan.updated_name', index=30,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='doc_plan.Plan.status', index=31,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_before', full_name='doc_plan.Plan.start_before', index=32,
      number=34, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_deadline', full_name='doc_plan.Plan.doc_deadline', index=33,
      number=35, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_cancel', full_name='doc_plan.Plan.doc_cancel', index=34,
      number=36, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='last_time', full_name='doc_plan.Plan.last_time', index=35,
      number=37, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='next_time', full_name='doc_plan.Plan.next_time', index=36,
      number=38, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_recreate', full_name='doc_plan.Plan.is_recreate', index=37,
      number=39, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='doc_plan.Plan.sub_type', index=38,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='doc_plan.Plan.store_type', index=39,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extend', full_name='doc_plan.Plan.extend', index=40,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tz', full_name='doc_plan.Plan.tz', index=41,
      number=43, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calc_by_safety_qty', full_name='doc_plan.Plan.calc_by_safety_qty', index=42,
      number=44, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2962,
  serialized_end=4066,
)


_PLANINFO = _descriptor.Descriptor(
  name='PlanInfo',
  full_name='doc_plan.PlanInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='doc_plan.PlanInfo.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='doc_plan.PlanInfo.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.PlanInfo.plan_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='before', full_name='doc_plan.PlanInfo.before', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_inventory', full_name='doc_plan.PlanInfo.calculate_inventory', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='doc_plan.PlanInfo.code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='doc_plan.PlanInfo.name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='method', full_name='doc_plan.PlanInfo.method', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='month_method', full_name='doc_plan.PlanInfo.month_method', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_month_method', full_name='doc_plan.PlanInfo.sub_month_method', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='week_method', full_name='doc_plan.PlanInfo.week_method', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_week_method', full_name='doc_plan.PlanInfo.sub_week_method', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='day_method', full_name='doc_plan.PlanInfo.day_method', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_day_method', full_name='doc_plan.PlanInfo.sub_day_method', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='interval', full_name='doc_plan.PlanInfo.interval', index=14,
      number=16, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_method', full_name='doc_plan.PlanInfo.branch_method', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_method', full_name='doc_plan.PlanInfo.product_method', index=16,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_store', full_name='doc_plan.PlanInfo.plan_store', index=17,
      number=19, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_branch', full_name='doc_plan.PlanInfo.plan_branch', index=18,
      number=20, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_product', full_name='doc_plan.PlanInfo.plan_product', index=19,
      number=21, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_category', full_name='doc_plan.PlanInfo.plan_category', index=20,
      number=22, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='doc_plan.PlanInfo.remark', index=21,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='doc_plan.PlanInfo.start', index=22,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='doc_plan.PlanInfo.end', index=23,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='doc_plan.PlanInfo.id', index=24,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='doc_plan.PlanInfo.created_at', index=25,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='doc_plan.PlanInfo.updated_at', index=26,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='doc_plan.PlanInfo.created_by', index=27,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='doc_plan.PlanInfo.updated_by', index=28,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='doc_plan.PlanInfo.created_name', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='doc_plan.PlanInfo.updated_name', index=30,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='doc_plan.PlanInfo.status', index=31,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_before', full_name='doc_plan.PlanInfo.start_before', index=32,
      number=34, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_deadline', full_name='doc_plan.PlanInfo.doc_deadline', index=33,
      number=35, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_cancel', full_name='doc_plan.PlanInfo.doc_cancel', index=34,
      number=36, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='last_time', full_name='doc_plan.PlanInfo.last_time', index=35,
      number=37, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='next_time', full_name='doc_plan.PlanInfo.next_time', index=36,
      number=38, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_recreate', full_name='doc_plan.PlanInfo.is_recreate', index=37,
      number=39, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='doc_plan.PlanInfo.sub_type', index=38,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='doc_plan.PlanInfo.store_type', index=39,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extend', full_name='doc_plan.PlanInfo.extend', index=40,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='doc_plan.PlanInfo.branch_type', index=41,
      number=44, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tz', full_name='doc_plan.PlanInfo.tz', index=42,
      number=45, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calc_by_safety_qty', full_name='doc_plan.PlanInfo.calc_by_safety_qty', index=43,
      number=46, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4069,
  serialized_end=5292,
)


_GETPLANRESPONSE = _descriptor.Descriptor(
  name='GetPlanResponse',
  full_name='doc_plan.GetPlanResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='doc_plan.GetPlanResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='doc_plan.GetPlanResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5294,
  serialized_end=5356,
)


_PUTPLANREQUEST = _descriptor.Descriptor(
  name='PutPlanRequest',
  full_name='doc_plan.PutPlanRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.PutPlanRequest.plan_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='doc_plan.PutPlanRequest.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.PutPlanRequest.plan_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='before', full_name='doc_plan.PutPlanRequest.before', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_inventory', full_name='doc_plan.PutPlanRequest.calculate_inventory', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='doc_plan.PutPlanRequest.code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='doc_plan.PutPlanRequest.name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='method', full_name='doc_plan.PutPlanRequest.method', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='month_method', full_name='doc_plan.PutPlanRequest.month_method', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_month_method', full_name='doc_plan.PutPlanRequest.sub_month_method', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='week_method', full_name='doc_plan.PutPlanRequest.week_method', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_week_method', full_name='doc_plan.PutPlanRequest.sub_week_method', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='day_method', full_name='doc_plan.PutPlanRequest.day_method', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_day_method', full_name='doc_plan.PutPlanRequest.sub_day_method', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='interval', full_name='doc_plan.PutPlanRequest.interval', index=14,
      number=16, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_method', full_name='doc_plan.PutPlanRequest.branch_method', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_method', full_name='doc_plan.PutPlanRequest.product_method', index=16,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='doc_plan.PutPlanRequest.store_ids', index=17,
      number=19, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='doc_plan.PutPlanRequest.branch_ids', index=18,
      number=20, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='doc_plan.PutPlanRequest.product_ids', index=19,
      number=21, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='doc_plan.PutPlanRequest.category_ids', index=20,
      number=22, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='doc_plan.PutPlanRequest.remark', index=21,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='doc_plan.PutPlanRequest.start', index=22,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='doc_plan.PutPlanRequest.end', index=23,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='only_remark', full_name='doc_plan.PutPlanRequest.only_remark', index=24,
      number=26, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_deadline', full_name='doc_plan.PutPlanRequest.doc_deadline', index=25,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_cancel', full_name='doc_plan.PutPlanRequest.doc_cancel', index=26,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_recreate', full_name='doc_plan.PutPlanRequest.is_recreate', index=27,
      number=29, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='doc_plan.PutPlanRequest.sub_type', index=28,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='doc_plan.PutPlanRequest.branch_type', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='doc_plan.PutPlanRequest.store_type', index=30,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extend', full_name='doc_plan.PutPlanRequest.extend', index=31,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='doc_plan.PutPlanRequest.domain', index=32,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module', full_name='doc_plan.PutPlanRequest.module', index=33,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tz', full_name='doc_plan.PutPlanRequest.tz', index=34,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calc_by_safety_qty', full_name='doc_plan.PutPlanRequest.calc_by_safety_qty', index=35,
      number=38, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5359,
  serialized_end=6192,
)


_PUTPLANRESPONSE = _descriptor.Descriptor(
  name='PutPlanResponse',
  full_name='doc_plan.PutPlanResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='doc_plan.PutPlanResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6194,
  serialized_end=6227,
)


_ACTIONPLANREQUEST = _descriptor.Descriptor(
  name='ActionPlanRequest',
  full_name='doc_plan.ActionPlanRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.ActionPlanRequest.plan_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='doc_plan.ActionPlanRequest.action', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.ActionPlanRequest.plan_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='doc_plan.ActionPlanRequest.domain', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module', full_name='doc_plan.ActionPlanRequest.module', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _ACTIONPLANREQUEST_ACTION,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6230,
  serialized_end=6418,
)


_ACTIONPLANRESPONSE = _descriptor.Descriptor(
  name='ActionPlanResponse',
  full_name='doc_plan.ActionPlanResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='doc_plan.ActionPlanResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6420,
  serialized_end=6456,
)


_CREATEDOCREQUEST = _descriptor.Descriptor(
  name='CreateDocRequest',
  full_name='doc_plan.CreateDocRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_date', full_name='doc_plan.CreateDocRequest.request_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.CreateDocRequest.plan_type', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='doc_plan.CreateDocRequest.sub_type', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='doc_plan.CreateDocRequest.domain', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module', full_name='doc_plan.CreateDocRequest.module', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6459,
  serialized_end=6596,
)


_CREATEDOCRESPONSE = _descriptor.Descriptor(
  name='CreateDocResponse',
  full_name='doc_plan.CreateDocResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='doc_plan.CreateDocResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='doc_plan.CreateDocResponse.request_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6598,
  serialized_end=6683,
)


_MODIFYPLANPRODUCTBYSTOREREQUEST = _descriptor.Descriptor(
  name='ModifyPlanProductByStoreRequest',
  full_name='doc_plan.ModifyPlanProductByStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.ModifyPlanProductByStoreRequest.plan_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='doc_plan.ModifyPlanProductByStoreRequest.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='doc_plan.ModifyPlanProductByStoreRequest.product_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='doc_plan.ModifyPlanProductByStoreRequest.category_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_method', full_name='doc_plan.ModifyPlanProductByStoreRequest.product_method', index=4,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='doc_plan.ModifyPlanProductByStoreRequest.reason', index=5,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.ModifyPlanProductByStoreRequest.plan_type', index=6,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='doc_plan.ModifyPlanProductByStoreRequest.domain', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module', full_name='doc_plan.ModifyPlanProductByStoreRequest.module', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6686,
  serialized_end=6888,
)


_MODIFYPLANPRODUCTBYSTORERESPONSE = _descriptor.Descriptor(
  name='ModifyPlanProductByStoreResponse',
  full_name='doc_plan.ModifyPlanProductByStoreResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='doc_plan.ModifyPlanProductByStoreResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6890,
  serialized_end=6940,
)


_GETPLANPRODUCTBYSTOREREQUEST = _descriptor.Descriptor(
  name='GetPlanProductByStoreRequest',
  full_name='doc_plan.GetPlanProductByStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.GetPlanProductByStoreRequest.plan_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='doc_plan.GetPlanProductByStoreRequest.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.GetPlanProductByStoreRequest.plan_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='doc_plan.GetPlanProductByStoreRequest.domain', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module', full_name='doc_plan.GetPlanProductByStoreRequest.module', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6942,
  serialized_end=7058,
)


_PLANPRODUCTBYSTORE = _descriptor.Descriptor(
  name='PlanProductByStore',
  full_name='doc_plan.PlanProductByStore',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='doc_plan.PlanProductByStore.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='doc_plan.PlanProductByStore.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='doc_plan.PlanProductByStore.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='doc_plan.PlanProductByStore.product_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='doc_plan.PlanProductByStore.created_by', index=4,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='doc_plan.PlanProductByStore.updated_by', index=5,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.PlanProductByStore.plan_id', index=6,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deleted', full_name='doc_plan.PlanProductByStore.deleted', index=7,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='doc_plan.PlanProductByStore.created_at', index=8,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='doc_plan.PlanProductByStore.updated_at', index=9,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='doc_plan.PlanProductByStore.partner_id', index=10,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='doc_plan.PlanProductByStore.id', index=11,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='doc_plan.PlanProductByStore.storage_type', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='doc_plan.PlanProductByStore.user_id', index=13,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='doc_plan.PlanProductByStore.created_name', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='doc_plan.PlanProductByStore.updated_name', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='modify_id', full_name='doc_plan.PlanProductByStore.modify_id', index=16,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='log_id', full_name='doc_plan.PlanProductByStore.log_id', index=17,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='doc_plan.PlanProductByStore.store_id', index=18,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7061,
  serialized_end=7505,
)


_PLANCATEGORYBYSTORE = _descriptor.Descriptor(
  name='PlanCategoryByStore',
  full_name='doc_plan.PlanCategoryByStore',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.PlanCategoryByStore.plan_id', index=0,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='doc_plan.PlanCategoryByStore.category_id', index=1,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='doc_plan.PlanCategoryByStore.category_code', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='doc_plan.PlanCategoryByStore.category_name', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_type', full_name='doc_plan.PlanCategoryByStore.category_type', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='doc_plan.PlanCategoryByStore.created_by', index=5,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='doc_plan.PlanCategoryByStore.updated_by', index=6,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deleted', full_name='doc_plan.PlanCategoryByStore.deleted', index=7,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='doc_plan.PlanCategoryByStore.created_at', index=8,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='doc_plan.PlanCategoryByStore.updated_at', index=9,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='doc_plan.PlanCategoryByStore.partner_id', index=10,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='doc_plan.PlanCategoryByStore.id', index=11,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='doc_plan.PlanCategoryByStore.user_id', index=12,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='doc_plan.PlanCategoryByStore.created_name', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='doc_plan.PlanCategoryByStore.updated_name', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='modify_id', full_name='doc_plan.PlanCategoryByStore.modify_id', index=15,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='log_id', full_name='doc_plan.PlanCategoryByStore.log_id', index=16,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='doc_plan.PlanCategoryByStore.store_id', index=17,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7508,
  serialized_end=7935,
)


_GETPLANPRODUCTBYSTORERESPONSE = _descriptor.Descriptor(
  name='GetPlanProductByStoreResponse',
  full_name='doc_plan.GetPlanProductByStoreResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='doc_plan.GetPlanProductByStoreResponse.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_product_by_store', full_name='doc_plan.GetPlanProductByStoreResponse.plan_product_by_store', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_category_by_store', full_name='doc_plan.GetPlanProductByStoreResponse.plan_category_by_store', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='doc_plan.GetPlanProductByStoreResponse.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_method', full_name='doc_plan.GetPlanProductByStoreResponse.product_method', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7938,
  serialized_end=8151,
)


_ACTIONPLANPRODUCTBYSTOREREQUEST = _descriptor.Descriptor(
  name='ActionPlanProductByStoreRequest',
  full_name='doc_plan.ActionPlanProductByStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.ActionPlanProductByStoreRequest.plan_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='doc_plan.ActionPlanProductByStoreRequest.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='doc_plan.ActionPlanProductByStoreRequest.action', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.ActionPlanProductByStoreRequest.plan_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='doc_plan.ActionPlanProductByStoreRequest.domain', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module', full_name='doc_plan.ActionPlanProductByStoreRequest.module', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _ACTIONPLANPRODUCTBYSTOREREQUEST_ACTION,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8154,
  serialized_end=8398,
)


_ACTIONPLANPRODUCTBYSTORERESPONSE = _descriptor.Descriptor(
  name='ActionPlanProductByStoreResponse',
  full_name='doc_plan.ActionPlanProductByStoreResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='doc_plan.ActionPlanProductByStoreResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8400,
  serialized_end=8450,
)


_GETSTOREPLANLISTREQUEST = _descriptor.Descriptor(
  name='GetStorePlanListRequest',
  full_name='doc_plan.GetStorePlanListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.GetStorePlanListRequest.plan_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='doc_plan.GetStorePlanListRequest.offset', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='doc_plan.GetStorePlanListRequest.limit', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.GetStorePlanListRequest.plan_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='doc_plan.GetStorePlanListRequest.domain', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module', full_name='doc_plan.GetStorePlanListRequest.module', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8452,
  serialized_end=8576,
)


_STOREPLANLIST = _descriptor.Descriptor(
  name='StorePlanList',
  full_name='doc_plan.StorePlanList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.StorePlanList.plan_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='doc_plan.StorePlanList.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='doc_plan.StorePlanList.store_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='doc_plan.StorePlanList.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='doc_plan.StorePlanList.store_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_quantity', full_name='doc_plan.StorePlanList.stocktake_quantity', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='add_quantity', full_name='doc_plan.StorePlanList.add_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reduce_quantity', full_name='doc_plan.StorePlanList.reduce_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='doc_plan.StorePlanList.updated_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='doc_plan.StorePlanList.updated_at', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_by_store', full_name='doc_plan.StorePlanList.product_by_store', index=10,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_by_store', full_name='doc_plan.StorePlanList.category_by_store', index=11,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_method', full_name='doc_plan.StorePlanList.product_method', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8579,
  serialized_end=8968,
)


_GETSTOREPLANLISTRESPONSE = _descriptor.Descriptor(
  name='GetStorePlanListResponse',
  full_name='doc_plan.GetStorePlanListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='doc_plan.GetStorePlanListResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='doc_plan.GetStorePlanListResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_rows', full_name='doc_plan.GetStorePlanListResponse.machining_center_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8971,
  serialized_end=9117,
)


_GETPLANSTREAMREQUEST = _descriptor.Descriptor(
  name='GetPlanStreamRequest',
  full_name='doc_plan.GetPlanStreamRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.GetPlanStreamRequest.plan_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.GetPlanStreamRequest.plan_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='doc_plan.GetPlanStreamRequest.store_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='get_store', full_name='doc_plan.GetPlanStreamRequest.get_store', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='get_product', full_name='doc_plan.GetPlanStreamRequest.get_product', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='doc_plan.GetPlanStreamRequest.domain', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module', full_name='doc_plan.GetPlanStreamRequest.module', index=6,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9120,
  serialized_end=9268,
)


_PLANSTREAMSTORE = _descriptor.Descriptor(
  name='PlanStreamStore',
  full_name='doc_plan.PlanStreamStore',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='target_date', full_name='doc_plan.PlanStreamStore.target_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_quantity', full_name='doc_plan.PlanStreamStore.plan_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='real_quantity', full_name='doc_plan.PlanStreamStore.real_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='doc_plan.PlanStreamStore.updated_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='doc_plan.PlanStreamStore.updated_at', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_infos', full_name='doc_plan.PlanStreamStore.store_infos', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9271,
  serialized_end=9502,
)


_UNCOMPLETEDSTORE = _descriptor.Descriptor(
  name='UncompletedStore',
  full_name='doc_plan.UncompletedStore',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='doc_plan.UncompletedStore.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='doc_plan.UncompletedStore.store_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9504,
  serialized_end=9560,
)


_PLANSTREAMPRODUCT = _descriptor.Descriptor(
  name='PlanStreamProduct',
  full_name='doc_plan.PlanStreamProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='target_date', full_name='doc_plan.PlanStreamProduct.target_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_quantity', full_name='doc_plan.PlanStreamProduct.plan_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='real_quantity', full_name='doc_plan.PlanStreamProduct.real_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='doc_plan.PlanStreamProduct.updated_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='doc_plan.PlanStreamProduct.updated_at', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='doc_plan.PlanStreamProduct.doc_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9563,
  serialized_end=9763,
)


_GETPLANSTREAMRESPONSE = _descriptor.Descriptor(
  name='GetPlanStreamResponse',
  full_name='doc_plan.GetPlanStreamResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='stream_stores', full_name='doc_plan.GetPlanStreamResponse.stream_stores', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stream_position_products', full_name='doc_plan.GetPlanStreamResponse.stream_position_products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='doc_plan.GetPlanStreamResponse.total', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9766,
  serialized_end=9925,
)


_GETPLANBYSTOREIDSREQUEST = _descriptor.Descriptor(
  name='GetPlanByStoreIDSRequest',
  full_name='doc_plan.GetPlanByStoreIDSRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.GetPlanByStoreIDSRequest.plan_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='doc_plan.GetPlanByStoreIDSRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='doc_plan.GetPlanByStoreIDSRequest.branch_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='doc_plan.GetPlanByStoreIDSRequest.store_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='doc_plan.GetPlanByStoreIDSRequest.offset', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='doc_plan.GetPlanByStoreIDSRequest.limit', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='method', full_name='doc_plan.GetPlanByStoreIDSRequest.method', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_recreate', full_name='doc_plan.GetPlanByStoreIDSRequest.is_recreate', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='doc_plan.GetPlanByStoreIDSRequest.sub_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='doc_plan.GetPlanByStoreIDSRequest.store_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='doc_plan.GetPlanByStoreIDSRequest.domain', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module', full_name='doc_plan.GetPlanByStoreIDSRequest.module', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9928,
  serialized_end=10166,
)


_STOREINFO = _descriptor.Descriptor(
  name='StoreInfo',
  full_name='doc_plan.StoreInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='doc_plan.StoreInfo.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='doc_plan.StoreInfo.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10168,
  serialized_end=10207,
)


_BRANCHINFO = _descriptor.Descriptor(
  name='BranchInfo',
  full_name='doc_plan.BranchInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='doc_plan.BranchInfo.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='doc_plan.BranchInfo.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10209,
  serialized_end=10249,
)


_PLANBYSTOREIDS = _descriptor.Descriptor(
  name='PlanByStoreIDS',
  full_name='doc_plan.PlanByStoreIDS',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='doc_plan.PlanByStoreIDS.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='doc_plan.PlanByStoreIDS.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.PlanByStoreIDS.plan_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='before', full_name='doc_plan.PlanByStoreIDS.before', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_inventory', full_name='doc_plan.PlanByStoreIDS.calculate_inventory', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='doc_plan.PlanByStoreIDS.code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='doc_plan.PlanByStoreIDS.name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='method', full_name='doc_plan.PlanByStoreIDS.method', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='month_method', full_name='doc_plan.PlanByStoreIDS.month_method', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_month_method', full_name='doc_plan.PlanByStoreIDS.sub_month_method', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='week_method', full_name='doc_plan.PlanByStoreIDS.week_method', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_week_method', full_name='doc_plan.PlanByStoreIDS.sub_week_method', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='day_method', full_name='doc_plan.PlanByStoreIDS.day_method', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_day_method', full_name='doc_plan.PlanByStoreIDS.sub_day_method', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='interval', full_name='doc_plan.PlanByStoreIDS.interval', index=14,
      number=16, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_method', full_name='doc_plan.PlanByStoreIDS.branch_method', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_method', full_name='doc_plan.PlanByStoreIDS.product_method', index=16,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_info', full_name='doc_plan.PlanByStoreIDS.store_info', index=17,
      number=19, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_info', full_name='doc_plan.PlanByStoreIDS.branch_info', index=18,
      number=20, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='doc_plan.PlanByStoreIDS.remark', index=19,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='doc_plan.PlanByStoreIDS.start', index=20,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='doc_plan.PlanByStoreIDS.end', index=21,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='doc_plan.PlanByStoreIDS.id', index=22,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='doc_plan.PlanByStoreIDS.created_at', index=23,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='doc_plan.PlanByStoreIDS.updated_at', index=24,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='doc_plan.PlanByStoreIDS.created_by', index=25,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='doc_plan.PlanByStoreIDS.updated_by', index=26,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='doc_plan.PlanByStoreIDS.created_name', index=27,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='doc_plan.PlanByStoreIDS.updated_name', index=28,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='doc_plan.PlanByStoreIDS.status', index=29,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_before', full_name='doc_plan.PlanByStoreIDS.start_before', index=30,
      number=34, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_deadline', full_name='doc_plan.PlanByStoreIDS.doc_deadline', index=31,
      number=35, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_cancel', full_name='doc_plan.PlanByStoreIDS.doc_cancel', index=32,
      number=36, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='last_time', full_name='doc_plan.PlanByStoreIDS.last_time', index=33,
      number=37, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='next_time', full_name='doc_plan.PlanByStoreIDS.next_time', index=34,
      number=38, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_recreate', full_name='doc_plan.PlanByStoreIDS.is_recreate', index=35,
      number=39, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='doc_plan.PlanByStoreIDS.sub_type', index=36,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extend', full_name='doc_plan.PlanByStoreIDS.extend', index=37,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='doc_plan.PlanByStoreIDS.store_type', index=38,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10252,
  serialized_end=11328,
)


_GETPLANBYSTOREIDSRESPONSE = _descriptor.Descriptor(
  name='GetPlanByStoreIDSResponse',
  full_name='doc_plan.GetPlanByStoreIDSResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='doc_plan.GetPlanByStoreIDSResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='doc_plan.GetPlanByStoreIDSResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11330,
  serialized_end=11412,
)


_PREVIEWDOCBYSTOREIDREQUEST = _descriptor.Descriptor(
  name='PreviewDocByStoreIDRequest',
  full_name='doc_plan.PreviewDocByStoreIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.PreviewDocByStoreIDRequest.plan_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='doc_plan.PreviewDocByStoreIDRequest.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.PreviewDocByStoreIDRequest.plan_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='doc_plan.PreviewDocByStoreIDRequest.domain', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module', full_name='doc_plan.PreviewDocByStoreIDRequest.module', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11414,
  serialized_end=11528,
)


_PREVIEWDOCPRODUCT_PRODUCTUNITS = _descriptor.Descriptor(
  name='ProductUnits',
  full_name='doc_plan.PreviewDocProduct.ProductUnits',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='doc_plan.PreviewDocProduct.ProductUnits.unit_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='doc_plan.PreviewDocProduct.ProductUnits.unit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='doc_plan.PreviewDocProduct.ProductUnits.unit_spec', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='doc_plan.PreviewDocProduct.ProductUnits.unit_rate', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11668,
  serialized_end=11756,
)

_PREVIEWDOCPRODUCT = _descriptor.Descriptor(
  name='PreviewDocProduct',
  full_name='doc_plan.PreviewDocProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_code', full_name='doc_plan.PreviewDocProduct.product_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='doc_plan.PreviewDocProduct.product_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='doc_plan.PreviewDocProduct.units', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model', full_name='doc_plan.PreviewDocProduct.model', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_PREVIEWDOCPRODUCT_PRODUCTUNITS, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11531,
  serialized_end=11756,
)


_PREVIEWDOCBYSTOREIDRESPONSE = _descriptor.Descriptor(
  name='PreviewDocByStoreIDResponse',
  full_name='doc_plan.PreviewDocByStoreIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='doc_plan.PreviewDocByStoreIDResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='doc_plan.PreviewDocByStoreIDResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11758,
  serialized_end=11845,
)


_GETPLANDOCSTATUSREQUEST = _descriptor.Descriptor(
  name='GetPlanDocStatusRequest',
  full_name='doc_plan.GetPlanDocStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.GetPlanDocStatusRequest.plan_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.GetPlanDocStatusRequest.plan_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='doc_plan.GetPlanDocStatusRequest.domain', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module', full_name='doc_plan.GetPlanDocStatusRequest.module', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='doc_plan.GetPlanDocStatusRequest.order_type_id', index=4,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11847,
  serialized_end=11963,
)


_PLANDOCSTATUS = _descriptor.Descriptor(
  name='PlanDocStatus',
  full_name='doc_plan.PlanDocStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='time', full_name='doc_plan.PlanDocStatus.time', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='time_around', full_name='doc_plan.PlanDocStatus.time_around', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_status', full_name='doc_plan.PlanDocStatus.start_status', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_status', full_name='doc_plan.PlanDocStatus.end_status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='doc_plan.PlanDocStatus.id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.PlanDocStatus.plan_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_filter', full_name='doc_plan.PlanDocStatus.doc_filter', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='doc_plan.PlanDocStatus.order_type_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='state', full_name='doc_plan.PlanDocStatus.state', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11966,
  serialized_end=12145,
)


_GETPLANDOCSTATUSRESPONSE = _descriptor.Descriptor(
  name='GetPlanDocStatusResponse',
  full_name='doc_plan.GetPlanDocStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='doc_plan.GetPlanDocStatusResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.GetPlanDocStatusResponse.plan_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12147,
  serialized_end=12229,
)


_ACTIONPLANDOCSTATUSREQUEST = _descriptor.Descriptor(
  name='ActionPlanDocStatusRequest',
  full_name='doc_plan.ActionPlanDocStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.ActionPlanDocStatusRequest.plan_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='doc_plan.ActionPlanDocStatusRequest.action', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='doc_plan.ActionPlanDocStatusRequest.rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='doc_plan.ActionPlanDocStatusRequest.request_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.ActionPlanDocStatusRequest.plan_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='doc_plan.ActionPlanDocStatusRequest.domain', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module', full_name='doc_plan.ActionPlanDocStatusRequest.module', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='doc_plan.ActionPlanDocStatusRequest.order_type_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _ACTIONPLANDOCSTATUSREQUEST_ACTION,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12232,
  serialized_end=12524,
)


_ACTIONPLANDOCSTATUSRESPONSE = _descriptor.Descriptor(
  name='ActionPlanDocStatusResponse',
  full_name='doc_plan.ActionPlanDocStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='doc_plan.ActionPlanDocStatusResponse.result', index=0,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12526,
  serialized_end=12571,
)


_GETPLANPOSITIONPRODUCTREQUEST = _descriptor.Descriptor(
  name='GetPlanPositionProductRequest',
  full_name='doc_plan.GetPlanPositionProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.GetPlanPositionProductRequest.plan_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='doc_plan.GetPlanPositionProductRequest.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='doc_plan.GetPlanPositionProductRequest.store_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.GetPlanPositionProductRequest.plan_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='doc_plan.GetPlanPositionProductRequest.domain', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module', full_name='doc_plan.GetPlanPositionProductRequest.module', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12574,
  serialized_end=12711,
)


_POSITIONPRODUCT = _descriptor.Descriptor(
  name='PositionProduct',
  full_name='doc_plan.PositionProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='doc_plan.PositionProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='doc_plan.PositionProduct.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='doc_plan.PositionProduct.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12713,
  serialized_end=12794,
)


_PLANPOSITIONCATEGORY = _descriptor.Descriptor(
  name='PlanPositionCategory',
  full_name='doc_plan.PlanPositionCategory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='category_id', full_name='doc_plan.PlanPositionCategory.category_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='doc_plan.PlanPositionCategory.category_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='doc_plan.PlanPositionCategory.category_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12796,
  serialized_end=12885,
)


_PLANPOSITIONPRODUCT = _descriptor.Descriptor(
  name='PlanPositionProduct',
  full_name='doc_plan.PlanPositionProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='position_id', full_name='doc_plan.PlanPositionProduct.position_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='doc_plan.PlanPositionProduct.position_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='doc_plan.PlanPositionProduct.position_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='doc_plan.PlanPositionProduct.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='categorys', full_name='doc_plan.PlanPositionProduct.categorys', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12888,
  serialized_end=13072,
)


_GETPLANPOSITIONPRODUCTRESPONSE = _descriptor.Descriptor(
  name='GetPlanPositionProductResponse',
  full_name='doc_plan.GetPlanPositionProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='doc_plan.GetPlanPositionProductResponse.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='doc_plan.GetPlanPositionProductResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='doc_plan.GetPlanPositionProductResponse.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13074,
  serialized_end=13185,
)


_UPDATEPOSITIONPRODUCTREQUEST = _descriptor.Descriptor(
  name='UpdatePositionProductRequest',
  full_name='doc_plan.UpdatePositionProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.UpdatePositionProductRequest.plan_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='doc_plan.UpdatePositionProductRequest.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='doc_plan.UpdatePositionProductRequest.rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.UpdatePositionProductRequest.plan_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='doc_plan.UpdatePositionProductRequest.domain', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module', full_name='doc_plan.UpdatePositionProductRequest.module', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13188,
  serialized_end=13349,
)


_PLANSTREAMPOSITIONPRODUCT = _descriptor.Descriptor(
  name='PlanStreamPositionProduct',
  full_name='doc_plan.PlanStreamPositionProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='position_id', full_name='doc_plan.PlanStreamPositionProduct.position_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='doc_plan.PlanStreamPositionProduct.position_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='doc_plan.PlanStreamPositionProduct.position_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stream_products', full_name='doc_plan.PlanStreamPositionProduct.stream_products', index=3,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='doc_plan.PlanStreamPositionProduct.total', index=4,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13352,
  serialized_end=13515,
)


_MACHININGCENTERPLANLIST_POSITIONPLANLIST = _descriptor.Descriptor(
  name='PositionPlanList',
  full_name='doc_plan.MachiningCenterPlanList.PositionPlanList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='position_id', full_name='doc_plan.MachiningCenterPlanList.PositionPlanList.position_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='doc_plan.MachiningCenterPlanList.PositionPlanList.position_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='doc_plan.MachiningCenterPlanList.PositionPlanList.position_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_quantity', full_name='doc_plan.MachiningCenterPlanList.PositionPlanList.stocktake_quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='add_quantity', full_name='doc_plan.MachiningCenterPlanList.PositionPlanList.add_quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reduce_quantity', full_name='doc_plan.MachiningCenterPlanList.PositionPlanList.reduce_quantity', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13857,
  serialized_end=14017,
)

_MACHININGCENTERPLANLIST = _descriptor.Descriptor(
  name='MachiningCenterPlanList',
  full_name='doc_plan.MachiningCenterPlanList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.MachiningCenterPlanList.plan_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='doc_plan.MachiningCenterPlanList.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='doc_plan.MachiningCenterPlanList.store_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='doc_plan.MachiningCenterPlanList.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='doc_plan.MachiningCenterPlanList.store_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='doc_plan.MachiningCenterPlanList.updated_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='doc_plan.MachiningCenterPlanList.updated_at', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_rows', full_name='doc_plan.MachiningCenterPlanList.position_rows', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_quantity', full_name='doc_plan.MachiningCenterPlanList.stocktake_quantity', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='add_quantity', full_name='doc_plan.MachiningCenterPlanList.add_quantity', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reduce_quantity', full_name='doc_plan.MachiningCenterPlanList.reduce_quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_MACHININGCENTERPLANLIST_POSITIONPLANLIST, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13518,
  serialized_end=14017,
)


_GETDOCPLANCALENDARREQUEST = _descriptor.Descriptor(
  name='GetDocPlanCalendarRequest',
  full_name='doc_plan.GetDocPlanCalendarRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.GetDocPlanCalendarRequest.plan_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='doc_plan.GetDocPlanCalendarRequest.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='doc_plan.GetDocPlanCalendarRequest.offset', index=2,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='doc_plan.GetDocPlanCalendarRequest.limit', index=3,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='doc_plan.GetDocPlanCalendarRequest.store_type', index=4,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='doc_plan.GetDocPlanCalendarRequest.start_date', index=5,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='doc_plan.GetDocPlanCalendarRequest.end_date', index=6,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='utc_offset', full_name='doc_plan.GetDocPlanCalendarRequest.utc_offset', index=7,
      number=10, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14020,
  serialized_end=14249,
)


_GETDOCPLANCALENDARRESPONSE = _descriptor.Descriptor(
  name='GetDocPlanCalendarResponse',
  full_name='doc_plan.GetDocPlanCalendarResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='doc_plan.GetDocPlanCalendarResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='doc_plan.GetDocPlanCalendarResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14251,
  serialized_end=14332,
)


_PLANCALENDAR = _descriptor.Descriptor(
  name='PlanCalendar',
  full_name='doc_plan.PlanCalendar',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.PlanCalendar.plan_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='before', full_name='doc_plan.PlanCalendar.before', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_type', full_name='doc_plan.PlanCalendar.plan_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='doc_plan.PlanCalendar.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='doc_plan.PlanCalendar.name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='method', full_name='doc_plan.PlanCalendar.method', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_date_list', full_name='doc_plan.PlanCalendar.plan_date_list', index=6,
      number=10, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_time', full_name='doc_plan.PlanCalendar.plan_time', index=7,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='doc_plan.PlanCalendar.start', index=8,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='doc_plan.PlanCalendar.end', index=9,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='doc_plan.PlanCalendar.status', index=10,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='doc_plan.PlanCalendar.store_type', index=11,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='time_around', full_name='doc_plan.PlanCalendar.time_around', index=12,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='control_status', full_name='doc_plan.PlanCalendar.control_status', index=13,
      number=17, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14335,
  serialized_end=14706,
)


_GETPLANPRODUCTBYIDREQUEST = _descriptor.Descriptor(
  name='GetPlanProductByIDRequest',
  full_name='doc_plan.GetPlanProductByIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plan_id', full_name='doc_plan.GetPlanProductByIDRequest.plan_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='doc_plan.GetPlanProductByIDRequest.return_fields', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='doc_plan.GetPlanProductByIDRequest.limit', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='doc_plan.GetPlanProductByIDRequest.offset', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='doc_plan.GetPlanProductByIDRequest.include_total', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='doc_plan.GetPlanProductByIDRequest.domain', index=5,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='module', full_name='doc_plan.GetPlanProductByIDRequest.module', index=6,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14709,
  serialized_end=14862,
)


_GETPLANPRODUCTBYIDRESPONSE = _descriptor.Descriptor(
  name='GetPlanProductByIDResponse',
  full_name='doc_plan.GetPlanProductByIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='doc_plan.GetPlanProductByIDResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='doc_plan.GetPlanProductByIDResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14864,
  serialized_end=14939,
)

_CREATEPLANREQUEST.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEPLANREQUEST.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEPLANREQUEST.fields_by_name['doc_deadline'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEPLANREQUEST.fields_by_name['doc_cancel'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANBRANCH.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANBRANCH.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANSTORE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANSTORE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANCATEGORY.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANCATEGORY.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLAN.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLAN.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLAN.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLAN.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLAN.fields_by_name['start_before'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLAN.fields_by_name['doc_deadline'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLAN.fields_by_name['doc_cancel'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLAN.fields_by_name['last_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLAN.fields_by_name['next_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANINFO.fields_by_name['plan_store'].message_type = _PLANSTORE
_PLANINFO.fields_by_name['plan_branch'].message_type = _PLANBRANCH
_PLANINFO.fields_by_name['plan_product'].message_type = _PLANPRODUCT
_PLANINFO.fields_by_name['plan_category'].message_type = _PLANCATEGORY
_PLANINFO.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANINFO.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANINFO.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANINFO.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANINFO.fields_by_name['start_before'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANINFO.fields_by_name['doc_deadline'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANINFO.fields_by_name['doc_cancel'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANINFO.fields_by_name['last_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANINFO.fields_by_name['next_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPLANRESPONSE.fields_by_name['rows'].message_type = _PLAN
_PUTPLANREQUEST.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PUTPLANREQUEST.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PUTPLANREQUEST.fields_by_name['doc_deadline'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PUTPLANREQUEST.fields_by_name['doc_cancel'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ACTIONPLANREQUEST.fields_by_name['action'].enum_type = _ACTIONPLANREQUEST_ACTION
_ACTIONPLANREQUEST_ACTION.containing_type = _ACTIONPLANREQUEST
_CREATEDOCREQUEST.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEDOCRESPONSE.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANPRODUCTBYSTORE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANPRODUCTBYSTORE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANCATEGORYBYSTORE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANCATEGORYBYSTORE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPLANPRODUCTBYSTORERESPONSE.fields_by_name['plan_product_by_store'].message_type = _PLANPRODUCTBYSTORE
_GETPLANPRODUCTBYSTORERESPONSE.fields_by_name['plan_category_by_store'].message_type = _PLANCATEGORYBYSTORE
_ACTIONPLANPRODUCTBYSTOREREQUEST.fields_by_name['action'].enum_type = _ACTIONPLANPRODUCTBYSTOREREQUEST_ACTION
_ACTIONPLANPRODUCTBYSTOREREQUEST_ACTION.containing_type = _ACTIONPLANPRODUCTBYSTOREREQUEST
_STOREPLANLIST.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOREPLANLIST.fields_by_name['product_by_store'].message_type = _PLANPRODUCTBYSTORE
_STOREPLANLIST.fields_by_name['category_by_store'].message_type = _PLANCATEGORYBYSTORE
_GETSTOREPLANLISTRESPONSE.fields_by_name['rows'].message_type = _STOREPLANLIST
_GETSTOREPLANLISTRESPONSE.fields_by_name['machining_center_rows'].message_type = _MACHININGCENTERPLANLIST
_PLANSTREAMSTORE.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANSTREAMSTORE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANSTREAMSTORE.fields_by_name['store_infos'].message_type = _UNCOMPLETEDSTORE
_PLANSTREAMPRODUCT.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANSTREAMPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPLANSTREAMRESPONSE.fields_by_name['stream_stores'].message_type = _PLANSTREAMSTORE
_GETPLANSTREAMRESPONSE.fields_by_name['stream_position_products'].message_type = _PLANSTREAMPOSITIONPRODUCT
_PLANBYSTOREIDS.fields_by_name['store_info'].message_type = _STOREINFO
_PLANBYSTOREIDS.fields_by_name['branch_info'].message_type = _BRANCHINFO
_PLANBYSTOREIDS.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANBYSTOREIDS.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANBYSTOREIDS.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANBYSTOREIDS.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANBYSTOREIDS.fields_by_name['start_before'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANBYSTOREIDS.fields_by_name['doc_deadline'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANBYSTOREIDS.fields_by_name['doc_cancel'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANBYSTOREIDS.fields_by_name['last_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANBYSTOREIDS.fields_by_name['next_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPLANBYSTOREIDSRESPONSE.fields_by_name['rows'].message_type = _PLANBYSTOREIDS
_PREVIEWDOCPRODUCT_PRODUCTUNITS.containing_type = _PREVIEWDOCPRODUCT
_PREVIEWDOCPRODUCT.fields_by_name['units'].message_type = _PREVIEWDOCPRODUCT_PRODUCTUNITS
_PREVIEWDOCBYSTOREIDRESPONSE.fields_by_name['rows'].message_type = _PREVIEWDOCPRODUCT
_GETPLANDOCSTATUSRESPONSE.fields_by_name['rows'].message_type = _PLANDOCSTATUS
_ACTIONPLANDOCSTATUSREQUEST.fields_by_name['action'].enum_type = _ACTIONPLANDOCSTATUSREQUEST_ACTION
_ACTIONPLANDOCSTATUSREQUEST.fields_by_name['rows'].message_type = _PLANDOCSTATUS
_ACTIONPLANDOCSTATUSREQUEST_ACTION.containing_type = _ACTIONPLANDOCSTATUSREQUEST
_PLANPOSITIONPRODUCT.fields_by_name['products'].message_type = _POSITIONPRODUCT
_PLANPOSITIONPRODUCT.fields_by_name['categorys'].message_type = _PLANPOSITIONCATEGORY
_GETPLANPOSITIONPRODUCTRESPONSE.fields_by_name['rows'].message_type = _PLANPOSITIONPRODUCT
_UPDATEPOSITIONPRODUCTREQUEST.fields_by_name['rows'].message_type = _PLANPOSITIONPRODUCT
_PLANSTREAMPOSITIONPRODUCT.fields_by_name['stream_products'].message_type = _PLANSTREAMPRODUCT
_MACHININGCENTERPLANLIST_POSITIONPLANLIST.containing_type = _MACHININGCENTERPLANLIST
_MACHININGCENTERPLANLIST.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_MACHININGCENTERPLANLIST.fields_by_name['position_rows'].message_type = _MACHININGCENTERPLANLIST_POSITIONPLANLIST
_GETDOCPLANCALENDARREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDOCPLANCALENDARREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDOCPLANCALENDARRESPONSE.fields_by_name['rows'].message_type = _PLANCALENDAR
_PLANCALENDAR.fields_by_name['plan_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANCALENDAR.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANCALENDAR.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PLANCALENDAR.fields_by_name['control_status'].message_type = _PLANDOCSTATUS
_GETPLANPRODUCTBYIDRESPONSE.fields_by_name['rows'].message_type = metadata_dot_product__pb2._PRODUCT
DESCRIPTOR.message_types_by_name['Null'] = _NULL
DESCRIPTOR.message_types_by_name['Pong'] = _PONG
DESCRIPTOR.message_types_by_name['CreatePlanRequest'] = _CREATEPLANREQUEST
DESCRIPTOR.message_types_by_name['CreatePlanResponse'] = _CREATEPLANRESPONSE
DESCRIPTOR.message_types_by_name['GetPlanByIDRequest'] = _GETPLANBYIDREQUEST
DESCRIPTOR.message_types_by_name['PlanBranch'] = _PLANBRANCH
DESCRIPTOR.message_types_by_name['PlanStore'] = _PLANSTORE
DESCRIPTOR.message_types_by_name['PlanProduct'] = _PLANPRODUCT
DESCRIPTOR.message_types_by_name['PlanCategory'] = _PLANCATEGORY
DESCRIPTOR.message_types_by_name['GetPlanRequest'] = _GETPLANREQUEST
DESCRIPTOR.message_types_by_name['Plan'] = _PLAN
DESCRIPTOR.message_types_by_name['PlanInfo'] = _PLANINFO
DESCRIPTOR.message_types_by_name['GetPlanResponse'] = _GETPLANRESPONSE
DESCRIPTOR.message_types_by_name['PutPlanRequest'] = _PUTPLANREQUEST
DESCRIPTOR.message_types_by_name['PutPlanResponse'] = _PUTPLANRESPONSE
DESCRIPTOR.message_types_by_name['ActionPlanRequest'] = _ACTIONPLANREQUEST
DESCRIPTOR.message_types_by_name['ActionPlanResponse'] = _ACTIONPLANRESPONSE
DESCRIPTOR.message_types_by_name['CreateDocRequest'] = _CREATEDOCREQUEST
DESCRIPTOR.message_types_by_name['CreateDocResponse'] = _CREATEDOCRESPONSE
DESCRIPTOR.message_types_by_name['ModifyPlanProductByStoreRequest'] = _MODIFYPLANPRODUCTBYSTOREREQUEST
DESCRIPTOR.message_types_by_name['ModifyPlanProductByStoreResponse'] = _MODIFYPLANPRODUCTBYSTORERESPONSE
DESCRIPTOR.message_types_by_name['GetPlanProductByStoreRequest'] = _GETPLANPRODUCTBYSTOREREQUEST
DESCRIPTOR.message_types_by_name['PlanProductByStore'] = _PLANPRODUCTBYSTORE
DESCRIPTOR.message_types_by_name['PlanCategoryByStore'] = _PLANCATEGORYBYSTORE
DESCRIPTOR.message_types_by_name['GetPlanProductByStoreResponse'] = _GETPLANPRODUCTBYSTORERESPONSE
DESCRIPTOR.message_types_by_name['ActionPlanProductByStoreRequest'] = _ACTIONPLANPRODUCTBYSTOREREQUEST
DESCRIPTOR.message_types_by_name['ActionPlanProductByStoreResponse'] = _ACTIONPLANPRODUCTBYSTORERESPONSE
DESCRIPTOR.message_types_by_name['GetStorePlanListRequest'] = _GETSTOREPLANLISTREQUEST
DESCRIPTOR.message_types_by_name['StorePlanList'] = _STOREPLANLIST
DESCRIPTOR.message_types_by_name['GetStorePlanListResponse'] = _GETSTOREPLANLISTRESPONSE
DESCRIPTOR.message_types_by_name['GetPlanStreamRequest'] = _GETPLANSTREAMREQUEST
DESCRIPTOR.message_types_by_name['PlanStreamStore'] = _PLANSTREAMSTORE
DESCRIPTOR.message_types_by_name['UncompletedStore'] = _UNCOMPLETEDSTORE
DESCRIPTOR.message_types_by_name['PlanStreamProduct'] = _PLANSTREAMPRODUCT
DESCRIPTOR.message_types_by_name['GetPlanStreamResponse'] = _GETPLANSTREAMRESPONSE
DESCRIPTOR.message_types_by_name['GetPlanByStoreIDSRequest'] = _GETPLANBYSTOREIDSREQUEST
DESCRIPTOR.message_types_by_name['StoreInfo'] = _STOREINFO
DESCRIPTOR.message_types_by_name['BranchInfo'] = _BRANCHINFO
DESCRIPTOR.message_types_by_name['PlanByStoreIDS'] = _PLANBYSTOREIDS
DESCRIPTOR.message_types_by_name['GetPlanByStoreIDSResponse'] = _GETPLANBYSTOREIDSRESPONSE
DESCRIPTOR.message_types_by_name['PreviewDocByStoreIDRequest'] = _PREVIEWDOCBYSTOREIDREQUEST
DESCRIPTOR.message_types_by_name['PreviewDocProduct'] = _PREVIEWDOCPRODUCT
DESCRIPTOR.message_types_by_name['PreviewDocByStoreIDResponse'] = _PREVIEWDOCBYSTOREIDRESPONSE
DESCRIPTOR.message_types_by_name['GetPlanDocStatusRequest'] = _GETPLANDOCSTATUSREQUEST
DESCRIPTOR.message_types_by_name['PlanDocStatus'] = _PLANDOCSTATUS
DESCRIPTOR.message_types_by_name['GetPlanDocStatusResponse'] = _GETPLANDOCSTATUSRESPONSE
DESCRIPTOR.message_types_by_name['ActionPlanDocStatusRequest'] = _ACTIONPLANDOCSTATUSREQUEST
DESCRIPTOR.message_types_by_name['ActionPlanDocStatusResponse'] = _ACTIONPLANDOCSTATUSRESPONSE
DESCRIPTOR.message_types_by_name['GetPlanPositionProductRequest'] = _GETPLANPOSITIONPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['PositionProduct'] = _POSITIONPRODUCT
DESCRIPTOR.message_types_by_name['PlanPositionCategory'] = _PLANPOSITIONCATEGORY
DESCRIPTOR.message_types_by_name['PlanPositionProduct'] = _PLANPOSITIONPRODUCT
DESCRIPTOR.message_types_by_name['GetPlanPositionProductResponse'] = _GETPLANPOSITIONPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['UpdatePositionProductRequest'] = _UPDATEPOSITIONPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['PlanStreamPositionProduct'] = _PLANSTREAMPOSITIONPRODUCT
DESCRIPTOR.message_types_by_name['MachiningCenterPlanList'] = _MACHININGCENTERPLANLIST
DESCRIPTOR.message_types_by_name['GetDocPlanCalendarRequest'] = _GETDOCPLANCALENDARREQUEST
DESCRIPTOR.message_types_by_name['GetDocPlanCalendarResponse'] = _GETDOCPLANCALENDARRESPONSE
DESCRIPTOR.message_types_by_name['PlanCalendar'] = _PLANCALENDAR
DESCRIPTOR.message_types_by_name['GetPlanProductByIDRequest'] = _GETPLANPRODUCTBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetPlanProductByIDResponse'] = _GETPLANPRODUCTBYIDRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Null = _reflection.GeneratedProtocolMessageType('Null', (_message.Message,), dict(
  DESCRIPTOR = _NULL,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.Null)
  ))
_sym_db.RegisterMessage(Null)

Pong = _reflection.GeneratedProtocolMessageType('Pong', (_message.Message,), dict(
  DESCRIPTOR = _PONG,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.Pong)
  ))
_sym_db.RegisterMessage(Pong)

CreatePlanRequest = _reflection.GeneratedProtocolMessageType('CreatePlanRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPLANREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.CreatePlanRequest)
  ))
_sym_db.RegisterMessage(CreatePlanRequest)

CreatePlanResponse = _reflection.GeneratedProtocolMessageType('CreatePlanResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPLANRESPONSE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.CreatePlanResponse)
  ))
_sym_db.RegisterMessage(CreatePlanResponse)

GetPlanByIDRequest = _reflection.GeneratedProtocolMessageType('GetPlanByIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPLANBYIDREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetPlanByIDRequest)
  ))
_sym_db.RegisterMessage(GetPlanByIDRequest)

PlanBranch = _reflection.GeneratedProtocolMessageType('PlanBranch', (_message.Message,), dict(
  DESCRIPTOR = _PLANBRANCH,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PlanBranch)
  ))
_sym_db.RegisterMessage(PlanBranch)

PlanStore = _reflection.GeneratedProtocolMessageType('PlanStore', (_message.Message,), dict(
  DESCRIPTOR = _PLANSTORE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PlanStore)
  ))
_sym_db.RegisterMessage(PlanStore)

PlanProduct = _reflection.GeneratedProtocolMessageType('PlanProduct', (_message.Message,), dict(
  DESCRIPTOR = _PLANPRODUCT,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PlanProduct)
  ))
_sym_db.RegisterMessage(PlanProduct)

PlanCategory = _reflection.GeneratedProtocolMessageType('PlanCategory', (_message.Message,), dict(
  DESCRIPTOR = _PLANCATEGORY,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PlanCategory)
  ))
_sym_db.RegisterMessage(PlanCategory)

GetPlanRequest = _reflection.GeneratedProtocolMessageType('GetPlanRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPLANREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetPlanRequest)
  ))
_sym_db.RegisterMessage(GetPlanRequest)

Plan = _reflection.GeneratedProtocolMessageType('Plan', (_message.Message,), dict(
  DESCRIPTOR = _PLAN,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.Plan)
  ))
_sym_db.RegisterMessage(Plan)

PlanInfo = _reflection.GeneratedProtocolMessageType('PlanInfo', (_message.Message,), dict(
  DESCRIPTOR = _PLANINFO,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PlanInfo)
  ))
_sym_db.RegisterMessage(PlanInfo)

GetPlanResponse = _reflection.GeneratedProtocolMessageType('GetPlanResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPLANRESPONSE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetPlanResponse)
  ))
_sym_db.RegisterMessage(GetPlanResponse)

PutPlanRequest = _reflection.GeneratedProtocolMessageType('PutPlanRequest', (_message.Message,), dict(
  DESCRIPTOR = _PUTPLANREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PutPlanRequest)
  ))
_sym_db.RegisterMessage(PutPlanRequest)

PutPlanResponse = _reflection.GeneratedProtocolMessageType('PutPlanResponse', (_message.Message,), dict(
  DESCRIPTOR = _PUTPLANRESPONSE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PutPlanResponse)
  ))
_sym_db.RegisterMessage(PutPlanResponse)

ActionPlanRequest = _reflection.GeneratedProtocolMessageType('ActionPlanRequest', (_message.Message,), dict(
  DESCRIPTOR = _ACTIONPLANREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.ActionPlanRequest)
  ))
_sym_db.RegisterMessage(ActionPlanRequest)

ActionPlanResponse = _reflection.GeneratedProtocolMessageType('ActionPlanResponse', (_message.Message,), dict(
  DESCRIPTOR = _ACTIONPLANRESPONSE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.ActionPlanResponse)
  ))
_sym_db.RegisterMessage(ActionPlanResponse)

CreateDocRequest = _reflection.GeneratedProtocolMessageType('CreateDocRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEDOCREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.CreateDocRequest)
  ))
_sym_db.RegisterMessage(CreateDocRequest)

CreateDocResponse = _reflection.GeneratedProtocolMessageType('CreateDocResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEDOCRESPONSE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.CreateDocResponse)
  ))
_sym_db.RegisterMessage(CreateDocResponse)

ModifyPlanProductByStoreRequest = _reflection.GeneratedProtocolMessageType('ModifyPlanProductByStoreRequest', (_message.Message,), dict(
  DESCRIPTOR = _MODIFYPLANPRODUCTBYSTOREREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.ModifyPlanProductByStoreRequest)
  ))
_sym_db.RegisterMessage(ModifyPlanProductByStoreRequest)

ModifyPlanProductByStoreResponse = _reflection.GeneratedProtocolMessageType('ModifyPlanProductByStoreResponse', (_message.Message,), dict(
  DESCRIPTOR = _MODIFYPLANPRODUCTBYSTORERESPONSE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.ModifyPlanProductByStoreResponse)
  ))
_sym_db.RegisterMessage(ModifyPlanProductByStoreResponse)

GetPlanProductByStoreRequest = _reflection.GeneratedProtocolMessageType('GetPlanProductByStoreRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPLANPRODUCTBYSTOREREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetPlanProductByStoreRequest)
  ))
_sym_db.RegisterMessage(GetPlanProductByStoreRequest)

PlanProductByStore = _reflection.GeneratedProtocolMessageType('PlanProductByStore', (_message.Message,), dict(
  DESCRIPTOR = _PLANPRODUCTBYSTORE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PlanProductByStore)
  ))
_sym_db.RegisterMessage(PlanProductByStore)

PlanCategoryByStore = _reflection.GeneratedProtocolMessageType('PlanCategoryByStore', (_message.Message,), dict(
  DESCRIPTOR = _PLANCATEGORYBYSTORE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PlanCategoryByStore)
  ))
_sym_db.RegisterMessage(PlanCategoryByStore)

GetPlanProductByStoreResponse = _reflection.GeneratedProtocolMessageType('GetPlanProductByStoreResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPLANPRODUCTBYSTORERESPONSE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetPlanProductByStoreResponse)
  ))
_sym_db.RegisterMessage(GetPlanProductByStoreResponse)

ActionPlanProductByStoreRequest = _reflection.GeneratedProtocolMessageType('ActionPlanProductByStoreRequest', (_message.Message,), dict(
  DESCRIPTOR = _ACTIONPLANPRODUCTBYSTOREREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.ActionPlanProductByStoreRequest)
  ))
_sym_db.RegisterMessage(ActionPlanProductByStoreRequest)

ActionPlanProductByStoreResponse = _reflection.GeneratedProtocolMessageType('ActionPlanProductByStoreResponse', (_message.Message,), dict(
  DESCRIPTOR = _ACTIONPLANPRODUCTBYSTORERESPONSE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.ActionPlanProductByStoreResponse)
  ))
_sym_db.RegisterMessage(ActionPlanProductByStoreResponse)

GetStorePlanListRequest = _reflection.GeneratedProtocolMessageType('GetStorePlanListRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOREPLANLISTREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetStorePlanListRequest)
  ))
_sym_db.RegisterMessage(GetStorePlanListRequest)

StorePlanList = _reflection.GeneratedProtocolMessageType('StorePlanList', (_message.Message,), dict(
  DESCRIPTOR = _STOREPLANLIST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.StorePlanList)
  ))
_sym_db.RegisterMessage(StorePlanList)

GetStorePlanListResponse = _reflection.GeneratedProtocolMessageType('GetStorePlanListResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOREPLANLISTRESPONSE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetStorePlanListResponse)
  ))
_sym_db.RegisterMessage(GetStorePlanListResponse)

GetPlanStreamRequest = _reflection.GeneratedProtocolMessageType('GetPlanStreamRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPLANSTREAMREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetPlanStreamRequest)
  ))
_sym_db.RegisterMessage(GetPlanStreamRequest)

PlanStreamStore = _reflection.GeneratedProtocolMessageType('PlanStreamStore', (_message.Message,), dict(
  DESCRIPTOR = _PLANSTREAMSTORE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PlanStreamStore)
  ))
_sym_db.RegisterMessage(PlanStreamStore)

UncompletedStore = _reflection.GeneratedProtocolMessageType('UncompletedStore', (_message.Message,), dict(
  DESCRIPTOR = _UNCOMPLETEDSTORE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.UncompletedStore)
  ))
_sym_db.RegisterMessage(UncompletedStore)

PlanStreamProduct = _reflection.GeneratedProtocolMessageType('PlanStreamProduct', (_message.Message,), dict(
  DESCRIPTOR = _PLANSTREAMPRODUCT,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PlanStreamProduct)
  ))
_sym_db.RegisterMessage(PlanStreamProduct)

GetPlanStreamResponse = _reflection.GeneratedProtocolMessageType('GetPlanStreamResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPLANSTREAMRESPONSE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetPlanStreamResponse)
  ))
_sym_db.RegisterMessage(GetPlanStreamResponse)

GetPlanByStoreIDSRequest = _reflection.GeneratedProtocolMessageType('GetPlanByStoreIDSRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPLANBYSTOREIDSREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetPlanByStoreIDSRequest)
  ))
_sym_db.RegisterMessage(GetPlanByStoreIDSRequest)

StoreInfo = _reflection.GeneratedProtocolMessageType('StoreInfo', (_message.Message,), dict(
  DESCRIPTOR = _STOREINFO,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.StoreInfo)
  ))
_sym_db.RegisterMessage(StoreInfo)

BranchInfo = _reflection.GeneratedProtocolMessageType('BranchInfo', (_message.Message,), dict(
  DESCRIPTOR = _BRANCHINFO,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.BranchInfo)
  ))
_sym_db.RegisterMessage(BranchInfo)

PlanByStoreIDS = _reflection.GeneratedProtocolMessageType('PlanByStoreIDS', (_message.Message,), dict(
  DESCRIPTOR = _PLANBYSTOREIDS,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PlanByStoreIDS)
  ))
_sym_db.RegisterMessage(PlanByStoreIDS)

GetPlanByStoreIDSResponse = _reflection.GeneratedProtocolMessageType('GetPlanByStoreIDSResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPLANBYSTOREIDSRESPONSE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetPlanByStoreIDSResponse)
  ))
_sym_db.RegisterMessage(GetPlanByStoreIDSResponse)

PreviewDocByStoreIDRequest = _reflection.GeneratedProtocolMessageType('PreviewDocByStoreIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _PREVIEWDOCBYSTOREIDREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PreviewDocByStoreIDRequest)
  ))
_sym_db.RegisterMessage(PreviewDocByStoreIDRequest)

PreviewDocProduct = _reflection.GeneratedProtocolMessageType('PreviewDocProduct', (_message.Message,), dict(

  ProductUnits = _reflection.GeneratedProtocolMessageType('ProductUnits', (_message.Message,), dict(
    DESCRIPTOR = _PREVIEWDOCPRODUCT_PRODUCTUNITS,
    __module__ = 'doc_plan_pb2'
    # @@protoc_insertion_point(class_scope:doc_plan.PreviewDocProduct.ProductUnits)
    ))
  ,
  DESCRIPTOR = _PREVIEWDOCPRODUCT,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PreviewDocProduct)
  ))
_sym_db.RegisterMessage(PreviewDocProduct)
_sym_db.RegisterMessage(PreviewDocProduct.ProductUnits)

PreviewDocByStoreIDResponse = _reflection.GeneratedProtocolMessageType('PreviewDocByStoreIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _PREVIEWDOCBYSTOREIDRESPONSE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PreviewDocByStoreIDResponse)
  ))
_sym_db.RegisterMessage(PreviewDocByStoreIDResponse)

GetPlanDocStatusRequest = _reflection.GeneratedProtocolMessageType('GetPlanDocStatusRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPLANDOCSTATUSREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetPlanDocStatusRequest)
  ))
_sym_db.RegisterMessage(GetPlanDocStatusRequest)

PlanDocStatus = _reflection.GeneratedProtocolMessageType('PlanDocStatus', (_message.Message,), dict(
  DESCRIPTOR = _PLANDOCSTATUS,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PlanDocStatus)
  ))
_sym_db.RegisterMessage(PlanDocStatus)

GetPlanDocStatusResponse = _reflection.GeneratedProtocolMessageType('GetPlanDocStatusResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPLANDOCSTATUSRESPONSE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetPlanDocStatusResponse)
  ))
_sym_db.RegisterMessage(GetPlanDocStatusResponse)

ActionPlanDocStatusRequest = _reflection.GeneratedProtocolMessageType('ActionPlanDocStatusRequest', (_message.Message,), dict(
  DESCRIPTOR = _ACTIONPLANDOCSTATUSREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.ActionPlanDocStatusRequest)
  ))
_sym_db.RegisterMessage(ActionPlanDocStatusRequest)

ActionPlanDocStatusResponse = _reflection.GeneratedProtocolMessageType('ActionPlanDocStatusResponse', (_message.Message,), dict(
  DESCRIPTOR = _ACTIONPLANDOCSTATUSRESPONSE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.ActionPlanDocStatusResponse)
  ))
_sym_db.RegisterMessage(ActionPlanDocStatusResponse)

GetPlanPositionProductRequest = _reflection.GeneratedProtocolMessageType('GetPlanPositionProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPLANPOSITIONPRODUCTREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetPlanPositionProductRequest)
  ))
_sym_db.RegisterMessage(GetPlanPositionProductRequest)

PositionProduct = _reflection.GeneratedProtocolMessageType('PositionProduct', (_message.Message,), dict(
  DESCRIPTOR = _POSITIONPRODUCT,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PositionProduct)
  ))
_sym_db.RegisterMessage(PositionProduct)

PlanPositionCategory = _reflection.GeneratedProtocolMessageType('PlanPositionCategory', (_message.Message,), dict(
  DESCRIPTOR = _PLANPOSITIONCATEGORY,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PlanPositionCategory)
  ))
_sym_db.RegisterMessage(PlanPositionCategory)

PlanPositionProduct = _reflection.GeneratedProtocolMessageType('PlanPositionProduct', (_message.Message,), dict(
  DESCRIPTOR = _PLANPOSITIONPRODUCT,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PlanPositionProduct)
  ))
_sym_db.RegisterMessage(PlanPositionProduct)

GetPlanPositionProductResponse = _reflection.GeneratedProtocolMessageType('GetPlanPositionProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPLANPOSITIONPRODUCTRESPONSE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetPlanPositionProductResponse)
  ))
_sym_db.RegisterMessage(GetPlanPositionProductResponse)

UpdatePositionProductRequest = _reflection.GeneratedProtocolMessageType('UpdatePositionProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPOSITIONPRODUCTREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.UpdatePositionProductRequest)
  ))
_sym_db.RegisterMessage(UpdatePositionProductRequest)

PlanStreamPositionProduct = _reflection.GeneratedProtocolMessageType('PlanStreamPositionProduct', (_message.Message,), dict(
  DESCRIPTOR = _PLANSTREAMPOSITIONPRODUCT,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PlanStreamPositionProduct)
  ))
_sym_db.RegisterMessage(PlanStreamPositionProduct)

MachiningCenterPlanList = _reflection.GeneratedProtocolMessageType('MachiningCenterPlanList', (_message.Message,), dict(

  PositionPlanList = _reflection.GeneratedProtocolMessageType('PositionPlanList', (_message.Message,), dict(
    DESCRIPTOR = _MACHININGCENTERPLANLIST_POSITIONPLANLIST,
    __module__ = 'doc_plan_pb2'
    # @@protoc_insertion_point(class_scope:doc_plan.MachiningCenterPlanList.PositionPlanList)
    ))
  ,
  DESCRIPTOR = _MACHININGCENTERPLANLIST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.MachiningCenterPlanList)
  ))
_sym_db.RegisterMessage(MachiningCenterPlanList)
_sym_db.RegisterMessage(MachiningCenterPlanList.PositionPlanList)

GetDocPlanCalendarRequest = _reflection.GeneratedProtocolMessageType('GetDocPlanCalendarRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDOCPLANCALENDARREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetDocPlanCalendarRequest)
  ))
_sym_db.RegisterMessage(GetDocPlanCalendarRequest)

GetDocPlanCalendarResponse = _reflection.GeneratedProtocolMessageType('GetDocPlanCalendarResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETDOCPLANCALENDARRESPONSE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetDocPlanCalendarResponse)
  ))
_sym_db.RegisterMessage(GetDocPlanCalendarResponse)

PlanCalendar = _reflection.GeneratedProtocolMessageType('PlanCalendar', (_message.Message,), dict(
  DESCRIPTOR = _PLANCALENDAR,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.PlanCalendar)
  ))
_sym_db.RegisterMessage(PlanCalendar)

GetPlanProductByIDRequest = _reflection.GeneratedProtocolMessageType('GetPlanProductByIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPLANPRODUCTBYIDREQUEST,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetPlanProductByIDRequest)
  ))
_sym_db.RegisterMessage(GetPlanProductByIDRequest)

GetPlanProductByIDResponse = _reflection.GeneratedProtocolMessageType('GetPlanProductByIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPLANPRODUCTBYIDRESPONSE,
  __module__ = 'doc_plan_pb2'
  # @@protoc_insertion_point(class_scope:doc_plan.GetPlanProductByIDResponse)
  ))
_sym_db.RegisterMessage(GetPlanProductByIDResponse)



_DOCPLAN = _descriptor.ServiceDescriptor(
  name='DocPlan',
  full_name='doc_plan.DocPlan',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=14942,
  serialized_end=17991,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreatePlan',
    full_name='doc_plan.DocPlan.CreatePlan',
    index=0,
    containing_service=None,
    input_type=_CREATEPLANREQUEST,
    output_type=_CREATEPLANRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\"\"/api/v2/doc_plan/{domain}/{module}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPlanByID',
    full_name='doc_plan.DocPlan.GetPlanByID',
    index=1,
    containing_service=None,
    input_type=_GETPLANBYIDREQUEST,
    output_type=_PLANINFO,
    serialized_options=_b('\202\323\344\223\002.\022,/api/v2/doc_plan/{domain}/{module}/{plan_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPlan',
    full_name='doc_plan.DocPlan.GetPlan',
    index=2,
    containing_service=None,
    input_type=_GETPLANREQUEST,
    output_type=_GETPLANRESPONSE,
    serialized_options=_b('\202\323\344\223\002$\022\"/api/v2/doc_plan/{domain}/{module}'),
  ),
  _descriptor.MethodDescriptor(
    name='PutPlan',
    full_name='doc_plan.DocPlan.PutPlan',
    index=3,
    containing_service=None,
    input_type=_PUTPLANREQUEST,
    output_type=_PUTPLANRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\032,/api/v2/doc_plan/{domain}/{module}/{plan_id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ActionPlan',
    full_name='doc_plan.DocPlan.ActionPlan',
    index=4,
    containing_service=None,
    input_type=_ACTIONPLANREQUEST,
    output_type=_ACTIONPLANRESPONSE,
    serialized_options=_b('\202\323\344\223\002:\0325/api/v2/doc_plan/{domain}/{module}/{plan_id}/{action}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CreateDoc',
    full_name='doc_plan.DocPlan.CreateDoc',
    index=5,
    containing_service=None,
    input_type=_CREATEDOCREQUEST,
    output_type=_CREATEDOCRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\"-/api/v2/doc_plan/{domain}/{module}/batch/init:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ModifyPlanProductByStore',
    full_name='doc_plan.DocPlan.ModifyPlanProductByStore',
    index=6,
    containing_service=None,
    input_type=_MODIFYPLANPRODUCTBYSTOREREQUEST,
    output_type=_MODIFYPLANPRODUCTBYSTORERESPONSE,
    serialized_options=_b('\202\323\344\223\0026\"1/api/v2/doc_plan/{domain}/{module}/modify/product:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPlanProductByStore',
    full_name='doc_plan.DocPlan.GetPlanProductByStore',
    index=7,
    containing_service=None,
    input_type=_GETPLANPRODUCTBYSTOREREQUEST,
    output_type=_GETPLANPRODUCTBYSTORERESPONSE,
    serialized_options=_b('\202\323\344\223\0029\0227/api/v2/doc_plan/{domain}/{module}/modify/product/query'),
  ),
  _descriptor.MethodDescriptor(
    name='ActionPlanProductByStore',
    full_name='doc_plan.DocPlan.ActionPlanProductByStore',
    index=8,
    containing_service=None,
    input_type=_ACTIONPLANPRODUCTBYSTOREREQUEST,
    output_type=_ACTIONPLANPRODUCTBYSTORERESPONSE,
    serialized_options=_b('\202\323\344\223\002=\0328/api/v2/doc_plan/{domain}/{module}/modify/product/status:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStorePlanList',
    full_name='doc_plan.DocPlan.GetStorePlanList',
    index=9,
    containing_service=None,
    input_type=_GETSTOREPLANLISTREQUEST,
    output_type=_GETSTOREPLANLISTRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\022//api/v2/doc_plan/{domain}/{module}/modify/store'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPlanStream',
    full_name='doc_plan.DocPlan.GetPlanStream',
    index=10,
    containing_service=None,
    input_type=_GETPLANSTREAMREQUEST,
    output_type=_GETPLANSTREAMRESPONSE,
    serialized_options=_b('\202\323\344\223\002/\022-/api/v2/doc_plan/{domain}/{module}/stream/log'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPlanByStoreIDS',
    full_name='doc_plan.DocPlan.GetPlanByStoreIDS',
    index=11,
    containing_service=None,
    input_type=_GETPLANBYSTOREIDSREQUEST,
    output_type=_GETPLANBYSTOREIDSRESPONSE,
    serialized_options=_b('\202\323\344\223\0024\"//api/v2/doc_plan/{domain}/{module}/by_store_ids:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='PreviewDocByStoreID',
    full_name='doc_plan.DocPlan.PreviewDocByStoreID',
    index=12,
    containing_service=None,
    input_type=_PREVIEWDOCBYSTOREIDREQUEST,
    output_type=_PREVIEWDOCBYSTOREIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0020\022./api/v2/doc_plan/{domain}/{module}/preview/doc'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPlanDocStatus',
    full_name='doc_plan.DocPlan.GetPlanDocStatus',
    index=13,
    containing_service=None,
    input_type=_GETPLANDOCSTATUSREQUEST,
    output_type=_GETPLANDOCSTATUSRESPONSE,
    serialized_options=_b('\202\323\344\223\0024\0222/api/v2/doc_plan/{domain}/{module}/plan_doc/status'),
  ),
  _descriptor.MethodDescriptor(
    name='ActionPlanDocStatus',
    full_name='doc_plan.DocPlan.ActionPlanDocStatus',
    index=14,
    containing_service=None,
    input_type=_ACTIONPLANDOCSTATUSREQUEST,
    output_type=_ACTIONPLANDOCSTATUSRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\"2/api/v2/doc_plan/{domain}/{module}/plan_doc/status:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPlanPositionProduct',
    full_name='doc_plan.DocPlan.GetPlanPositionProduct',
    index=15,
    containing_service=None,
    input_type=_GETPLANPOSITIONPRODUCTREQUEST,
    output_type=_GETPLANPOSITIONPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\"3/api/v2/doc_plan/{domain}/{module}/product/position:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdatePlanPositionProduct',
    full_name='doc_plan.DocPlan.UpdatePlanPositionProduct',
    index=16,
    containing_service=None,
    input_type=_UPDATEPOSITIONPRODUCTREQUEST,
    output_type=_ACTIONPLANDOCSTATUSRESPONSE,
    serialized_options=_b('\202\323\344\223\002@\032;/api/v2/doc_plan/{domain}/{module}/product/position/replace:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ActionPlanProductPosition',
    full_name='doc_plan.DocPlan.ActionPlanProductPosition',
    index=17,
    containing_service=None,
    input_type=_ACTIONPLANPRODUCTBYSTOREREQUEST,
    output_type=_ACTIONPLANDOCSTATUSRESPONSE,
    serialized_options=_b('\202\323\344\223\002?\032:/api/v2/doc_plan/{domain}/{module}/product/position/status:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetDocPlanCalendar',
    full_name='doc_plan.DocPlan.GetDocPlanCalendar',
    index=18,
    containing_service=None,
    input_type=_GETDOCPLANCALENDARREQUEST,
    output_type=_GETDOCPLANCALENDARRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\0220/api/v2/doc_plan/mobile/operation/calendar/query'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPlanProductByID',
    full_name='doc_plan.DocPlan.GetPlanProductByID',
    index=19,
    containing_service=None,
    input_type=_GETPLANPRODUCTBYIDREQUEST,
    output_type=_GETPLANPRODUCTBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0026\0224/api/v2/doc_plan/{domain}/{module}/{plan_id}/product'),
  ),
])
_sym_db.RegisterServiceDescriptor(_DOCPLAN)

DESCRIPTOR.services_by_name['DocPlan'] = _DOCPLAN

# @@protoc_insertion_point(module_scope)
