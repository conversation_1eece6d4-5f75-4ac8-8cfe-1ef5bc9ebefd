syntax = "proto3";

package doc_plan;
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "metadata/product.proto";

//计划服务
service DocPlan {
    //CreatePlan 创建计划1
    rpc CreatePlan (CreatePlanRequest) returns (CreatePlanResponse) {
        option (google.api.http) = { post: "/api/v2/doc_plan/{domain}/{module}" body: "*"};
    }
    //GetPlanByID 查询一个计划2
    rpc GetPlanByID (GetPlanByIDRequest) returns (PlanInfo) {
        option (google.api.http) = { get: "/api/v2/doc_plan/{domain}/{module}/{plan_id}" };
    }
    //GetPlan 计划查询3 增加业务type过滤
    rpc GetPlan (GetPlanRequest) returns (GetPlanResponse) {
        option (google.api.http) = { get: "/api/v2/doc_plan/{domain}/{module}" };
    }
    //PutPlan 更新计划4
    rpc PutPlan (PutPlanRequest) returns (PutPlanResponse) {
        option (google.api.http) = { put: "/api/v2/doc_plan/{domain}/{module}/{plan_id}" body: "*"};
    }
    //ActionPlan 确认、作废、回退、计划5
    rpc ActionPlan (ActionPlanRequest) returns (ActionPlanResponse) {
        option (google.api.http) = { put: "/api/v2/doc_plan/{domain}/{module}/{plan_id}/{action}" body: "*"};
    }
    //CreateDoc  schedule调用创建盘点批次(内部schedule dashboard调用，status=INITED）加recover，轮询没发成功的nsq13
    rpc CreateDoc (CreateDocRequest) returns (CreateDocResponse) {
        option (google.api.http) = { post: "/api/v2/doc_plan/{domain}/{module}/batch/init" body: "*"};
    }
    //ModifyPlanProductByStore 门店修改计划商品
    rpc ModifyPlanProductByStore (ModifyPlanProductByStoreRequest) returns (ModifyPlanProductByStoreResponse) {
        option (google.api.http) = { post: "/api/v2/doc_plan/{domain}/{module}/modify/product" body: "*"};
    }
    //GetPlanProductByStore 门店查询计划商品
    rpc GetPlanProductByStore (GetPlanProductByStoreRequest) returns (GetPlanProductByStoreResponse) {
        option (google.api.http) = { get: "/api/v2/doc_plan/{domain}/{module}/modify/product/query"};
    }
    //ActionPlanProductByStore 门店计划商品状态推动
    rpc ActionPlanProductByStore (ActionPlanProductByStoreRequest) returns (ActionPlanProductByStoreResponse) {
        option (google.api.http) = { put: "/api/v2/doc_plan/{domain}/{module}/modify/product/status" body: "*"};
    }
    //GetStorePlanList 门店计划列表查询
    rpc GetStorePlanList (GetStorePlanListRequest) returns (GetStorePlanListResponse) {
        option (google.api.http) = { get: "/api/v2/doc_plan/{domain}/{module}/modify/store"};
    }
    //GetPlanStream 计划执行记录
    rpc GetPlanStream (GetPlanStreamRequest) returns (GetPlanStreamResponse) {
        option (google.api.http) = { get: "/api/v2/doc_plan/{domain}/{module}/stream/log"};
    }
    //GetPlanByStoreIDS 支持门店数据权限查询(需要返回门店列表对应计划信息)
    rpc GetPlanByStoreIDS (GetPlanByStoreIDSRequest) returns (GetPlanByStoreIDSResponse) {
        option (google.api.http) = { post: "/api/v2/doc_plan/{domain}/{module}/by_store_ids" body: "*" };
    }
    //PreviewDocByStoreID 按业务规则预览门店单据商品（存在自动添加商品）
    rpc PreviewDocByStoreID (PreviewDocByStoreIDRequest) returns (PreviewDocByStoreIDResponse) {
        option (google.api.http) = { get: "/api/v2/doc_plan/{domain}/{module}/preview/doc"};
    }
    //GetPlanDocStatus单据状态变更查询
    rpc GetPlanDocStatus (GetPlanDocStatusRequest) returns (GetPlanDocStatusResponse) {
        option (google.api.http) = { get: "/api/v2/doc_plan/{domain}/{module}/plan_doc/status"};
    }
    //ActionPlanDocStatus单据状态变更存储更新
    rpc ActionPlanDocStatus (ActionPlanDocStatusRequest) returns (ActionPlanDocStatusResponse) {
        option (google.api.http) = { post: "/api/v2/doc_plan/{domain}/{module}/plan_doc/status" body: "*" };
    }

    // 计划安仓位做
    rpc GetPlanPositionProduct (GetPlanPositionProductRequest) returns (GetPlanPositionProductResponse) {
        option (google.api.http) = { post: "/api/v2/doc_plan/{domain}/{module}/product/position" body: "*" };
    }
    rpc UpdatePlanPositionProduct (UpdatePositionProductRequest) returns (ActionPlanDocStatusResponse) {
        option (google.api.http) = { put: "/api/v2/doc_plan/{domain}/{module}/product/position/replace" body: "*" };
    }
    //ActionPlanProductPosition
    rpc ActionPlanProductPosition (ActionPlanProductByStoreRequest) returns (ActionPlanDocStatusResponse) {
        option (google.api.http) = { put: "/api/v2/doc_plan/{domain}/{module}/product/position/status" body: "*"};
    }

    // 运营计划日历功能查询
    rpc GetDocPlanCalendar (GetDocPlanCalendarRequest) returns (GetDocPlanCalendarResponse) {
        option (google.api.http) = { get: "/api/v2/doc_plan/mobile/operation/calendar/query"};
    }
    // 查询计划里配置的商品(供导出使用)
    rpc GetPlanProductByID (GetPlanProductByIDRequest) returns (GetPlanProductByIDResponse) {
        option (google.api.http) = { get: "/api/v2/doc_plan/{domain}/{module}/{plan_id}/product" };
    }
}

message Null {
}

message Pong {
    string msg = 1;
}
message CreatePlanRequest {
    //请求ID
    uint64 request_id = 1;
    uint64 partner_id = 2;
    string plan_type = 3;
    //提前几天
    uint32 before = 4;
    //是否计算库存
    bool calculate_inventory = 5;
    //计划编码
    string code = 6;
    //计划名字
    string name = 7;
    //整个计划循环规则信息
    // 循环类型
    string method = 9;
    //月循环规则
    string month_method = 10;
    //循环规则对应未来规则间隔
    string sub_month_method = 11;
    //周循环规则
    string week_method = 12;
    //循环规则对应未来规则
    string sub_week_method = 13;
    //日循环规则
    string day_method = 14;
    //循环规则对应未来规则间隔
    string sub_day_method = 15;
    uint32 interval = 16;
    //整个计划循环规则信息
    string branch_method = 17;
    string product_method = 18;
    repeated uint64 store_ids = 19;
    repeated uint64 branch_ids = 20;
    repeated uint64 product_ids = 21;
    repeated uint64 category_ids = 22;
    //备注
    string remark = 23;
    //计划开始时间
    google.protobuf.Timestamp start = 24;
    //计划结束时间
    google.protobuf.Timestamp end = 25;
    //单据限制时间
    google.protobuf.Timestamp doc_deadline = 26;
    google.protobuf.Timestamp doc_cancel = 27;
    //不定期重盘
    bool is_recreate = 28;
    //子业务类型
    string sub_type = 29;
    //区域录入方式
    string branch_type = 30;
    // 门店类型：
    //  STORE: 门店(直营)
    //  WAREHOUSE: 仓库
    //  MACHINING_CENTER: 加工中心
    //  FRS_STORE: 加盟店
    string store_type = 31;
    string extend = 32;
    // 功能模块场景: (每个接口必传以做权限校验)
    // 1. store
    // 2. whs
    // 3. mfy
    // 4. frs-store
    // 5. store-mgt
    // 6. whs-mgt
    // 7. mfy-mgt
    // 8. frs-mgt
    string domain = 33;
    // 请求的模块：(每个接口必传以做权限校验)
    // 1. order
    // 2. st
    // 3. loss
    // 4. irr-st
    string module = 35;
    // 时区
    string tz = 36;
    // 是否使用安全库存计算订货量
    bool calc_by_safety_qty = 37;
}
message CreatePlanResponse {
    uint64 plan_id = 1;
    bool result = 2;
}
message GetPlanByIDRequest {
    //计划id
    uint64 plan_id = 1;
    //是否包含商品
    bool include_product = 2;
    //是否包含商品类型
    bool include_category = 3;
    //是否包含管理区域
    bool include_branch = 4;
    //是否包含门店
    bool include_store = 5;
    // 门店类型：
    //  STORE: 门店(直营)
    //  WAREHOUSE: 仓库
    //  MACHINING_CENTER: 加工中心
    //  FRS_STORE: 加盟店
    string store_type = 6;
    string plan_type = 7;
    // 功能模块场景: (每个接口必传以做权限校验)
    // 1. store
    // 2. whs
    // 3. mfy
    // 4. frs-store
    // 5. store-mgt
    // 6. whs-mgt
    // 7. mfy-mgt
    // 8. frs-mgt
    string domain = 10;
    // 请求的模块：(每个接口必传以做权限校验)
    // 1. order
    // 2. st
    // 3. loss
    // 4. irr-st
    string module = 15;
}
message PlanBranch {
    //门店id
    uint64 branch_id = 1;
    //计划id
    uint64 plan_id = 2;
    bool deleted = 3;
    //门店code
    string branch_code = 4;
    //门店名字
    string branch_name = 5;
    //门店类型
    string branch_type = 6;
    //创建者id
    uint64 created_by = 8;
    //更新者id
    uint64 updated_by = 9;
    //创建时间
    google.protobuf.Timestamp created_at = 10;
    //更新时间
    google.protobuf.Timestamp updated_at = 11;
    uint64 partner_id = 12;
    uint64 id = 13;
    uint64 user_id = 14;
    string created_name = 15;
    string updated_name = 16;
}
message PlanStore {
    //门店id
    uint64 store_id = 1;
    //计划id
    uint64 plan_id = 2;
    bool deleted = 3;
    //门店code
    string store_code = 4;
    //门店名字
    string store_name = 5;
    // 门店类型：
    //  STORE: 门店(直营)
    //  WAREHOUSE: 仓库
    //  MACHINING_CENTER: 加工中心
    //  FRS_STORE: 加盟店
    string store_type = 6;
    //创建者id
    uint64 created_by = 8;
    //更新者id
    uint64 updated_by = 9;
    //创建时间
    google.protobuf.Timestamp created_at = 10;
    //更新时间
    google.protobuf.Timestamp updated_at = 11;
    uint64 partner_id = 12;
    uint64 id = 13;
    uint64 user_id = 14;
    string created_name = 15;
    string updated_name = 16;
}
message PlanProduct {
    //商品id
    uint64 product_id = 1;
    //商品code
    string product_code = 2;
    //商品名字
    string product_name = 3;
    //商品类型
    string product_type = 4;
    //创建者id
    uint64 created_by = 6;
    //更新者id
    uint64 updated_by = 7;
    //计划id
    uint64 plan_id = 8;
    bool deleted = 9;
    //创建时间
    google.protobuf.Timestamp created_at = 10;
    //更新时间
    google.protobuf.Timestamp updated_at = 11;
    uint64 partner_id = 12;
    uint64 id = 13;
    string storage_type = 14;
    uint64 user_id = 15;
    string created_name = 16;
    string updated_name = 17;
}
message PlanCategory {
    //计划id
    uint64 plan_id = 2;
    //商品类别id
    uint64 category_id = 3;
    //商品类别code
    string category_code = 4;
    //商品类别名字
    string category_name = 5;
    //商品类别类型
    string category_type = 6;
    //创建者id
    uint64 created_by = 7;
    //更新者id
    uint64 updated_by = 8;
    bool deleted = 9;
    //创建时间
    google.protobuf.Timestamp created_at = 10;
    //更新时间
    google.protobuf.Timestamp updated_at = 11;
    uint64 partner_id = 12;
    uint64 id = 13;
    uint64 user_id = 14;
    string created_name = 15;
    string updated_name = 16;
}
message GetPlanRequest {
    string plan_type = 1;
    //计划状态
    repeated string status = 2;
    repeated uint64 branch_ids = 3;
    repeated uint64 store_ids = 4;
    //分页开始处
    uint32 offset = 5;
    //返回条数限制
    uint32 limit = 6;
    // 循环类型
    string method = 7;
    bool is_recreate = 8;
    // 盘点属性：计划-PLAN/不定期-R
    string sub_type = 9;
    // 门店类型：
    //  STORE: 门店(直营)
    //  WAREHOUSE: 仓库
    //  MACHINING_CENTER: 加工中心
    //  FRS_STORE: 加盟店
    string store_type = 10;
    // 功能模块场景: (每个接口必传以做权限校验)
    // 1. store
    // 2. whs
    // 3. mfy
    // 4. frs-store
    // 5. store-mgt
    // 6. whs-mgt
    // 7. mfy-mgt
    // 8. frs-mgt
    string domain = 20;
    // 请求的模块：(每个接口必传以做权限校验)
    // 1. order
    // 2. st
    // 3. loss
    // 4. irr-st
    string module = 25;
}
message Plan {
    //请求ID
    uint64 request_id = 1;
    uint64 partner_id = 2;
    string plan_type = 3;
    //提前几天
    uint32 before = 4;
    //是否计算库存
    bool calculate_inventory = 5;
    //计划编码
    string code = 6;
    //计划名字
    string name = 7;
    //整个计划循环规则信息
    // 循环类型
    string method = 9;
    //月循环规则
    string month_method = 10;
    //循环规则对应未来规则间隔
    string sub_month_method = 11;
    //周循环规则
    string week_method = 12;
    //循环规则对应未来规则
    string sub_week_method = 13;
    //日循环规则
    string day_method = 14;
    //循环规则对应未来规则间隔
    string sub_day_method = 15;
    uint32 interval = 16;
    //整个计划循环规则信息
    string branch_method = 17;
    string product_method = 18;
    repeated uint64 store_ids = 19;
    repeated uint64 branch_ids = 20;
    repeated uint64 product_ids = 21;
    repeated uint64 category_ids = 22;
    //备注
    string remark = 23;
    //计划开始时间
    google.protobuf.Timestamp start = 24;
    //计划结束时间
    google.protobuf.Timestamp end = 25;
    uint64 id = 26;
    google.protobuf.Timestamp created_at = 27;
    google.protobuf.Timestamp updated_at = 28;
    uint64 created_by = 29;
    uint64 updated_by = 30;
    string created_name = 31;
    string updated_name = 32;
    string status = 33;
    google.protobuf.Timestamp start_before = 34;
    google.protobuf.Timestamp doc_deadline = 35;
    google.protobuf.Timestamp doc_cancel = 36;
    google.protobuf.Timestamp last_time = 37;
    google.protobuf.Timestamp next_time = 38;
    bool is_recreate = 39;
    string sub_type = 40;
    // 门店类型：
    //  STORE: 门店(直营)
    //  WAREHOUSE: 仓库
    //  MACHINING_CENTER: 加工中心
    //  FRS_STORE: 加盟店
    string store_type = 41;
    string extend = 42;
    // 时区
    string tz = 43;
    bool calc_by_safety_qty =44;
}
message PlanInfo {
    //请求ID
    uint64 request_id = 1;
    uint64 partner_id = 2;
    string plan_type = 3;
    //提前几天
    uint32 before = 4;
    //是否计算库存
    bool calculate_inventory = 5;
    //计划编码
    string code = 6;
    //计划名字
    string name = 7;
    //整个计划循环规则信息
    // 循环类型
    string method = 9;
    //月循环规则
    string month_method = 10;
    //循环规则对应未来规则间隔
    string sub_month_method = 11;
    //周循环规则
    string week_method = 12;
    //循环规则对应未来规则
    string sub_week_method = 13;
    //日循环规则
    string day_method = 14;
    //循环规则对应未来规则间隔
    string sub_day_method = 15;
    uint32 interval = 16;
    //整个计划循环规则信息
    string branch_method = 17;
    string product_method = 18;
    //计划商品列表
    repeated PlanStore plan_store = 19;
    repeated PlanBranch plan_branch = 20;
    repeated PlanProduct plan_product = 21;
    repeated PlanCategory plan_category = 22;
    //备注
    string remark = 23;
    //计划开始时间
    google.protobuf.Timestamp start = 24;
    //计划结束时间
    google.protobuf.Timestamp end = 25;
    uint64 id = 26;
    google.protobuf.Timestamp created_at = 27;
    google.protobuf.Timestamp updated_at = 28;
    uint64 created_by = 29;
    uint64 updated_by = 30;
    string created_name = 31;
    string updated_name = 32;
    string status = 33;
    google.protobuf.Timestamp start_before = 34;
    google.protobuf.Timestamp doc_deadline = 35;
    google.protobuf.Timestamp doc_cancel = 36;
    google.protobuf.Timestamp last_time = 37;
    google.protobuf.Timestamp next_time = 38;
    bool is_recreate = 39;
    string sub_type = 40;
    // 门店类型：
    //  STORE: 门店(直营)
    //  WAREHOUSE: 仓库
    //  MACHINING_CENTER: 加工中心
    //  FRS_STORE: 加盟店
    string store_type = 41;
    string extend = 42;
    // 区域录入方式
    string branch_type = 44;
    // 时区
    string tz = 45;
    bool calc_by_safety_qty = 46;
}
message GetPlanResponse {
    //计划列表
    repeated Plan rows = 1;
    uint32 total = 2;
}
message PutPlanRequest {
    //计划ID
    uint64 plan_id = 1;
    uint64 partner_id = 2;
    string plan_type = 3;
    //提前几天
    uint32 before = 4;
    //是否计算库存
    bool calculate_inventory = 5;
    //计划编码
    string code = 6;
    //计划名字
    string name = 7;
    //整个计划循环规则信息
    // 循环类型
    string method = 9;
    //月循环规则
    string month_method = 10;
    //循环规则对应未来规则间隔
    string sub_month_method = 11;
    //周循环规则
    string week_method = 12;
    //循环规则对应未来规则
    string sub_week_method = 13;
    //日循环规则
    string day_method = 14;
    //循环规则对应未来规则间隔
    string sub_day_method = 15;
    uint32 interval = 16;
    //整个计划循环规则信息
    string branch_method = 17;
    string product_method = 18;
    repeated uint64 store_ids = 19;
    repeated uint64 branch_ids = 20;
    repeated uint64 product_ids = 21;
    repeated uint64 category_ids = 22;
    //备注
    string remark = 23;
    //计划开始时间
    google.protobuf.Timestamp start = 24;
    //计划结束时间
    google.protobuf.Timestamp end = 25;
    //只修改备注
    bool only_remark = 26;
    google.protobuf.Timestamp doc_deadline = 27;
    google.protobuf.Timestamp doc_cancel = 28;
    bool is_recreate = 29;
    string sub_type = 30;
    //区域录入方式
    string branch_type = 31;
    // 门店类型：
    //  STORE: 门店(直营)
    //  WAREHOUSE: 仓库
    //  MACHINING_CENTER: 加工中心
    //  FRS_STORE: 加盟店
    string store_type = 32;
    string extend = 33;
    // 功能模块场景: (每个接口必传以做权限校验)
    // 1. store
    // 2. whs
    // 3. mfy
    // 4. frs-store
    // 5. store-mgt
    // 6. whs-mgt
    // 7. mfy-mgt
    // 8. frs-mgt
    string domain = 35;
    // 请求的模块：(每个接口必传以做权限校验)
    // 1. order
    // 2. st
    // 3. loss
    // 4. irr-st
    string module = 36;
    // 时区
    string tz = 37;
    // 是否使用安全库存计算订货量
    bool calc_by_safety_qty = 38;
}
message PutPlanResponse {
    bool result = 1;
}
message ActionPlanRequest {
    //计划ID
    uint64 plan_id = 1;
    //动作
    enum Action {
        confirm = 0;
        cancel = 1;
        callback = 2;
    }
    Action action = 2;
    string plan_type = 3;
    // 功能模块场景: (每个接口必传以做权限校验)
    // 1. store
    // 2. whs
    // 3. mfy
    // 4. frs-store
    // 5. store-mgt
    // 6. whs-mgt
    // 7. mfy-mgt
    // 8. frs-mgt
    string domain = 5;
    // 请求的模块：(每个接口必传以做权限校验)
    // 1. order
    // 2. st
    // 3. loss
    // 4. irr-st
    string module = 6;
}
message ActionPlanResponse {
    bool result = 1;
}
message CreateDocRequest {
    //请求时间
    google.protobuf.Timestamp request_date = 1;
    repeated string plan_type = 2;
    //支持子业务执行控制
    repeated string sub_type = 3;
    // 功能模块场景: (每个接口必传以做权限校验)
    // 1. store
    // 2. whs
    // 3. mfy
    // 4. frs-store
    // 5. store-mgt
    // 6. whs-mgt
    // 7. mfy-mgt
    // 8. frs-mgt
    string domain = 4;
    // 请求的模块：(每个接口必传以做权限校验)
    // 1. order
    // 2. st
    // 3. loss
    // 4. irr-st
    string module = 5;
}
message CreateDocResponse {
    bool result = 1;
    //请求时间
    google.protobuf.Timestamp request_date = 2;
}
message ModifyPlanProductByStoreRequest {
    uint64 plan_id = 1;
    uint64 store_id = 2;
    repeated uint64 product_ids = 3;
    repeated uint64 category_ids = 4;
    // 商品修改方式: 全部 'ALL'/自定义 'SELF'
    string product_method = 10;
    string reason = 5;
    string plan_type = 6;
    // 功能模块场景: (每个接口必传以做权限校验)
    // 1. store
    // 2. whs
    // 3. mfy
    // 4. frs-store
    // 5. store-mgt
    // 6. whs-mgt
    // 7. mfy-mgt
    // 8. frs-mgt
    string domain = 8;
    // 请求的模块：(每个接口必传以做权限校验)
    // 1. order
    // 2. st
    // 3. loss
    // 4. irr-st
    string module = 9;
}
message ModifyPlanProductByStoreResponse {
    bool result = 1;
}
message GetPlanProductByStoreRequest {
    uint64 plan_id = 1;
    uint64 store_id = 2;
    string plan_type = 3;
    // 功能模块场景: (每个接口必传以做权限校验)
    // 1. store
    // 2. whs
    // 3. mfy
    // 4. frs-store
    // 5. store-mgt
    // 6. whs-mgt
    // 7. mfy-mgt
    // 8. frs-mgt
    string domain = 5;
    // 请求的模块：(每个接口必传以做权限校验)
    // 1. order
    // 2. st
    // 3. loss
    // 4. irr-st
    string module = 6;
}
message PlanProductByStore {
    //商品id
    uint64 product_id = 1;
    //商品code
    string product_code = 2;
    //商品名字
    string product_name = 3;
    //商品类型
    string product_type = 4;
    //创建者id
    uint64 created_by = 6;
    //更新者id
    uint64 updated_by = 7;
    //计划id
    uint64 plan_id = 8;
    bool deleted = 9;
    //创建时间
    google.protobuf.Timestamp created_at = 10;
    //更新时间
    google.protobuf.Timestamp updated_at = 11;
    uint64 partner_id = 12;
    uint64 id = 13;
    string storage_type = 14;
    uint64 user_id = 15;
    string created_name = 16;
    string updated_name = 17;
    uint64 modify_id = 18;
    uint64 log_id = 19;
    uint64 store_id = 20;
}
message PlanCategoryByStore {
    //计划id
    uint64 plan_id = 2;
    //商品类别id
    uint64 category_id = 3;
    //商品类别code
    string category_code = 4;
    //商品类别名字
    string category_name = 5;
    //商品类别类型
    string category_type = 6;
    //创建者id
    uint64 created_by = 7;
    //更新者id
    uint64 updated_by = 8;
    bool deleted = 9;
    //创建时间
    google.protobuf.Timestamp created_at = 10;
    //更新时间
    google.protobuf.Timestamp updated_at = 11;
    uint64 partner_id = 12;
    uint64 id = 13;
    uint64 user_id = 14;
    string created_name = 15;
    string updated_name = 16;
    uint64 modify_id = 17;
    uint64 log_id = 18;
    uint64 store_id = 19;
}
message GetPlanProductByStoreResponse {
    uint64 store_id = 1;
    repeated PlanProductByStore plan_product_by_store = 2;
    repeated PlanCategoryByStore plan_category_by_store = 3;
    string status = 4;
    string product_method = 5;
}
message ActionPlanProductByStoreRequest {
    uint64 plan_id = 1;
    uint64 store_id = 2;
    //动作
    enum Action {
        //0不操作
        null = 0;
        submit = 1;
        approve = 2;
        callback = 3;
    }
    Action action = 3;
    string plan_type = 4;
    // 功能模块场景: (每个接口必传以做权限校验)
    // 1. store
    // 2. whs
    // 3. mfy
    // 4. frs-store
    // 5. store-mgt
    // 6. whs-mgt
    // 7. mfy-mgt
    // 8. frs-mgt
    string domain = 5;
    // 请求的模块：(每个接口必传以做权限校验)
    // 1. order
    // 2. st
    // 3. loss
    // 4. irr-st
    string module = 6;
}
message ActionPlanProductByStoreResponse {
    bool result = 1;
}
message GetStorePlanListRequest {
    uint64 plan_id = 1;
    int64 offset = 2;
    int64 limit = 3;
    string plan_type = 4;
    // 功能模块场景: (每个接口必传以做权限校验)
    // 1. store
    // 2. whs
    // 3. mfy
    // 4. frs-store
    // 5. store-mgt
    // 6. whs-mgt
    // 7. mfy-mgt
    // 8. frs-mgt
    string domain = 5;
    // 请求的模块：(每个接口必传以做权限校验)
    // 1. order
    // 2. st
    // 3. loss
    // 4. irr-st
    string module = 6;
}
message StorePlanList {
    uint64 plan_id = 1;
    string status = 2;
    uint64 store_id = 3;
    string store_code = 4;
    string store_name = 5;
    double stocktake_quantity = 6;
    double add_quantity = 7;
    double reduce_quantity = 8;
    string updated_name = 9;
    //更新时间
    google.protobuf.Timestamp updated_at = 10;
    repeated PlanProductByStore product_by_store = 11;
    repeated PlanCategoryByStore category_by_store = 12;
    string product_method = 13;
}
message GetStorePlanListResponse {
    repeated StorePlanList rows = 1;
    uint64 total = 2;
    repeated MachiningCenterPlanList machining_center_rows = 3;
}
message GetPlanStreamRequest {
    string plan_type = 1;
    uint64 plan_id = 2;
    uint64 store_id = 3;
    bool get_store = 4;
    bool get_product = 5;
    // 功能模块场景: (每个接口必传以做权限校验)
    // 1. store
    // 2. whs
    // 3. mfy
    // 4. frs-store
    // 5. store-mgt
    // 6. whs-mgt
    // 7. mfy-mgt
    // 8. frs-mgt
    string domain = 6;
    // 请求的模块：(每个接口必传以做权限校验)
    // 1. order
    // 2. st
    // 3. loss
    // 4. irr-st
    string module = 8;
}
message PlanStreamStore {
    google.protobuf.Timestamp target_date = 1;
    double plan_quantity = 2;
    double real_quantity = 3;
    string updated_name = 4;
    //更新时间
    google.protobuf.Timestamp updated_at = 5;
    repeated UncompletedStore store_infos = 6;
}
message UncompletedStore {
    uint64 store_id = 1;
    string store_name = 2;
}
message PlanStreamProduct {
    google.protobuf.Timestamp target_date = 1;
    double plan_quantity = 2;
    double real_quantity = 3;
    string updated_name = 4;
    //更新时间
    google.protobuf.Timestamp updated_at = 5;
    uint64 doc_id = 6;
}
message GetPlanStreamResponse {
    repeated PlanStreamStore stream_stores = 1;
    repeated PlanStreamPositionProduct stream_position_products = 2;
    uint64 total = 3;
}
message GetPlanByStoreIDSRequest {
    string plan_type = 1;
    //计划状态
    repeated string status = 2;
    repeated uint64 branch_ids = 3;
    repeated uint64 store_ids = 4;
    //分页开始处
    uint32 offset = 5;
    //返回条数限制
    uint32 limit = 6;
    // 循环类型
    string method = 7;
    bool is_recreate = 8;
    string sub_type = 9;
    // 门店类型：
    //  STORE: 门店(直营)
    //  WAREHOUSE: 仓库
    //  MACHINING_CENTER: 加工中心
    //  FRS_STORE: 加盟店
    string store_type = 10;
    // 功能模块场景: (每个接口必传以做权限校验)
    // 1. store
    // 2. whs
    // 3. mfy
    // 4. frs-store
    // 5. store-mgt
    // 6. whs-mgt
    // 7. mfy-mgt
    // 8. frs-mgt
    string domain = 11;
    // 请求的模块：(每个接口必传以做权限校验)
    // 1. order
    // 2. st
    // 3. loss
    // 4. irr-st
    string module = 12;
}
message StoreInfo {
    string code = 1;
    string name = 2;
}
message BranchInfo {
    string code = 1;
    string name = 2;
}
message PlanByStoreIDS {
    //请求ID
    uint64 request_id = 1;
    uint64 partner_id = 2;
    string plan_type = 3;
    //提前几天
    uint32 before = 4;
    //是否计算库存
    bool calculate_inventory = 5;
    //计划编码
    string code = 6;
    //计划名字
    string name = 7;
    //整个计划循环规则信息
    // 循环类型
    string method = 9;
    //月循环规则
    string month_method = 10;
    //循环规则对应未来规则间隔
    string sub_month_method = 11;
    //周循环规则
    string week_method = 12;
    //循环规则对应未来规则
    string sub_week_method = 13;
    //日循环规则
    string day_method = 14;
    //循环规则对应未来规则间隔
    string sub_day_method = 15;
    uint32 interval = 16;
    //整个计划循环规则信息
    string branch_method = 17;
    string product_method = 18;
    repeated StoreInfo store_info = 19;
    repeated BranchInfo branch_info = 20;
    //备注
    string remark = 23;
    //计划开始时间
    google.protobuf.Timestamp start = 24;
    //计划结束时间
    google.protobuf.Timestamp end = 25;
    uint64 id = 26;
    google.protobuf.Timestamp created_at = 27;
    google.protobuf.Timestamp updated_at = 28;
    uint64 created_by = 29;
    uint64 updated_by = 30;
    string created_name = 31;
    string updated_name = 32;
    string status = 33;
    google.protobuf.Timestamp start_before = 34;
    google.protobuf.Timestamp doc_deadline = 35;
    google.protobuf.Timestamp doc_cancel = 36;
    google.protobuf.Timestamp last_time = 37;
    google.protobuf.Timestamp next_time = 38;
    bool is_recreate = 39;
    string sub_type = 40;
    string extend = 41;
    // 门店录入方式 STORE/WAREHOUSE/MACHINING_CENTER
    string store_type = 42;
}
message GetPlanByStoreIDSResponse {
    //计划列表
    repeated PlanByStoreIDS rows = 1;
    uint32 total = 2;
}
message PreviewDocByStoreIDRequest {
    uint64 plan_id = 1;
    uint64 store_id = 2;
    string plan_type = 3;
    // 功能模块场景: (每个接口必传以做权限校验)
    // 1. store
    // 2. whs
    // 3. mfy
    // 4. frs-store
    // 5. store-mgt
    // 6. whs-mgt
    // 7. mfy-mgt
    // 8. frs-mgt
    string domain = 5;
    string module = 6;
}
message PreviewDocProduct {
    message ProductUnits {
        uint64 unit_id = 1;
        string unit_name = 2;
        string unit_spec = 3;
        double unit_rate = 4;
    }
    string product_code = 1;
    string product_name = 2;
    repeated ProductUnits units = 3;
    string model = 4;
}
message PreviewDocByStoreIDResponse {
    repeated PreviewDocProduct rows = 1;
    uint32 total = 2;
}
message GetPlanDocStatusRequest {
    uint64 plan_id = 1;
    string plan_type = 2;
    // 功能模块场景: (每个接口必传以做权限校验)
    // 1. store
    // 2. whs
    // 3. mfy
    // 4. frs-store
    // 5. store-mgt
    // 6. whs-mgt
    // 7. mfy-mgt
    // 8. frs-mgt
    string domain = 3;
    string module = 5;
    uint64 order_type_id = 6;
}
message PlanDocStatus {
    string time = 1;
    // Yesterday  前日
    // Today      当日
    // Tomorrow   次日
    string time_around = 2;
    repeated string start_status = 3;
    string end_status = 4;
    uint64 id = 5;
    uint64 plan_id = 6;
    // 单据过滤条件：
    // 空单据: NULL
    // 非空单据: NOT_NULL
    // 所有不限制: ALL
    string doc_filter = 7;
    uint64 order_type_id = 8;
    string state = 9;
}
message GetPlanDocStatusResponse {
    repeated PlanDocStatus rows = 1;
    uint64 plan_id = 2;
}
message ActionPlanDocStatusRequest {
    uint64 plan_id = 1;
    //动作
    enum Action {
        //0不操作
        null = 0;
        add = 1;
        update = 2;
        delete = 3;
    }
    Action action = 2;
    repeated PlanDocStatus rows = 3;
    uint64 request_id = 4;
    string plan_type = 5;
    // 功能模块场景: (每个接口必传以做权限校验)
    // 1. store
    // 2. whs
    // 3. mfy
    // 4. frs-store
    // 5. store-mgt
    // 6. whs-mgt
    // 7. mfy-mgt
    // 8. frs-mgt
    string domain = 6;
    string module = 7;
    uint64 order_type_id = 8;
}
message ActionPlanDocStatusResponse {
    string result = 2;
}
message GetPlanPositionProductRequest {
    uint64 plan_id = 1;
    uint64 store_id = 2;
    string store_type = 3;
    string plan_type = 4;
    // 功能模块场景: (每个接口必传以做权限校验)
    // 1. store
    // 2. whs
    // 3. mfy
    // 4. frs-store
    // 5. store-mgt
    // 6. whs-mgt
    // 7. mfy-mgt
    // 8. frs-mgt
    string domain = 5;
    string module = 6;
}
message PositionProduct {
    uint64 product_id = 1;
    string product_code = 2;
    string product_name = 3;
}
message PlanPositionCategory {
    uint64 category_id = 1;
    string category_code = 2;
    string category_name = 3;
}
message PlanPositionProduct {
    uint64 position_id = 1;
    string position_code = 2;
    string position_name = 3;
    repeated PositionProduct products = 4;
    repeated PlanPositionCategory categorys = 5;
}
message GetPlanPositionProductResponse {
    uint64 store_id = 1;
    repeated PlanPositionProduct rows = 2;
    string status = 3;
}
message UpdatePositionProductRequest {
    uint64 plan_id = 1;
    uint64 store_id = 2;
    repeated PlanPositionProduct rows = 3;
    string plan_type = 4;
    // 功能模块场景: (每个接口必传以做权限校验)
    // 1. store
    // 2. whs
    // 3. mfy
    // 4. frs-store
    // 5. store-mgt
    // 6. whs-mgt
    // 7. mfy-mgt
    // 8. frs-mgt
    string domain = 5;
    string module = 6;
}
message PlanStreamPositionProduct {
    uint64 position_id = 1;
    string position_code = 2;
    string position_name = 3;
    repeated PlanStreamProduct stream_products = 5;
    uint64 total = 6;
}
//
message MachiningCenterPlanList {
    message PositionPlanList {
        uint64 position_id = 1;
        string position_code = 2;
        string position_name = 3;
        double stocktake_quantity = 4;
        double add_quantity = 5;
        double reduce_quantity = 6;
    }
    uint64 plan_id = 1;
    string status = 2;
    uint64 store_id = 3;
    string store_code = 4;
    string store_name = 5;
    string updated_name = 6;
    //更新时间
    google.protobuf.Timestamp updated_at = 7;
    repeated PositionPlanList position_rows = 8;
    double stocktake_quantity = 9;
    double add_quantity = 10;
    double reduce_quantity = 11;
}

message GetDocPlanCalendarRequest {
    // 计划类型ORDER/STOCKTAKE/ADJUST
    string plan_type = 1;
    uint64 store_id = 2;
    //分页开始处
    uint32 offset = 5;
    //返回条数限制
    uint32 limit = 6;
    // 门店类型：
    //  STORE: 门店(直营)
    //  WAREHOUSE: 仓库
    //  MACHINING_CENTER: 加工中心
    //  FRS_STORE: 加盟店
    string store_type = 7;
    // 查询开始时间
    google.protobuf.Timestamp start_date = 8;
    // 查询结束时间
    google.protobuf.Timestamp end_date = 9;
    uint32 utc_offset = 10;
}

message GetDocPlanCalendarResponse {
    // 计划日历列表
    repeated PlanCalendar rows = 1;
    uint32 total = 2;
}

message PlanCalendar {
    // 计划ID
    uint64 plan_id = 1;
    // 生单提前量
    uint32 before = 2;
    // 计划类型ORDER/STOCKTAKE/ADJUST
    string plan_type = 3;
    //计划编码
    string code = 4;
    //计划名字
    string name = 5;
    // 整个计划循环规则信息
    // 循环类型
    string method = 6;
    // 给定时间范围内生单日期
    repeated string plan_date_list = 10;
    // 生单时间
    google.protobuf.Timestamp plan_time = 11;
    //计划开始时间
    google.protobuf.Timestamp start = 12;
    //计划结束时间
    google.protobuf.Timestamp end = 13;
    string status = 14;
    // 门店类型：
    //  STORE: 门店(直营)
    //  WAREHOUSE: 仓库
    //  MACHINING_CENTER: 加工中心
    //  FRS_STORE: 加盟店
    string store_type = 15;
    // 计划变更时间计算偏移量(Yesterday/Today/Tomorrow)
    string time_around = 16;
    repeated PlanDocStatus control_status = 17;
}

message GetPlanProductByIDRequest {
    //计划id
    uint64 plan_id = 1;
    // 除code和relation之外需要返回的商品字段, 多个以逗号隔开
    string return_fields = 2;
    // 分页大小
    int32 limit = 3;
    // 跳过行数
    int32 offset = 4;
    // 是否包含总数
    bool include_total = 5;
    // 功能模块场景: (每个接口必传以做权限校验)
    // 1. store
    // 2. whs
    // 3. mfy
    // 4. frs-store
    // 5. store-mgt
    // 6. whs-mgt
    // 7. mfy-mgt
    // 8. frs-mgt
    string domain = 10;
    // 请求的模块：(每个接口必传以做权限校验)
    // 1. order
    // 2. st
    // 3. loss
    // 4. irr-st
    string module = 15;
}

message GetPlanProductByIDResponse {
    repeated product.Product rows = 1;
    uint32 total = 2;
}